// Type definitions for gsap 3
// Project: https://gsap.com/
// Definitions by: <PERSON> <https://github.com/jackdoyle>
//                 <PERSON> <https://github.com/OSUblake>
//                 <PERSON> <https://github.com/dipscom>
//                 <PERSON> <https://github.com/Zach<PERSON>>
// Definitions: https://github.com/greensock/GSAP

/// <reference path="animation.d.ts"/>
/// <reference path="custom-bounce.d.ts"/>
/// <reference path="custom-ease.d.ts"/>
/// <reference path="custom-wiggle.d.ts"/>
/// <reference path="css-plugin.d.ts"/>
/// <reference path="css-rule-plugin.d.ts"/>
/// <reference path="draggable.d.ts"/>
/// <reference path="draw-svg-plugin.d.ts"/>
/// <reference path="ease.d.ts"/>
/// <reference path="easel-plugin.d.ts"/>
/// <reference path="flip.d.ts"/>
/// <reference path="gs-dev-tools.d.ts"/>
/// <reference path="gsap-plugins.d.ts"/>
/// <reference path="gsap-utils.d.ts"/>
/// <reference path="inertia-plugin.d.ts"/>
/// <reference path="morph-svg-plugin.d.ts"/>
/// <reference path="motion-path-plugin.d.ts"/>
/// <reference path="motion-path-helper.d.ts"/>
/// <reference path="observer.d.ts"/>
/// <reference path="physics-2d-plugin.d.ts"/>
/// <reference path="physics-props-plugin.d.ts"/>
/// <reference path="pixi-plugin.d.ts"/>
/// <reference path="scramble-text-plugin.d.ts"/>
/// <reference path="scroll-to-plugin.d.ts"/>
/// <reference path="scroll-trigger.d.ts"/>
/// <reference path="scroll-smoother.d.ts"/>
/// <reference path="split-text.d.ts"/>
/// <reference path="text-plugin.d.ts"/>
/// <reference path="timeline.d.ts"/>
/// <reference path="tween.d.ts"/>
/// <reference path="utils/velocity-tracker.d.ts"/>
/// <reference path="gsap-core.d.ts"/>

// Global types
type GSAPDraggableVars = Draggable.Vars;
type GSAPAnimation = gsap.core.Animation;
type GSAPCallback = gsap.Callback;
type GSAPDistributeConfig = gsap.utils.DistributeConfig;
type GSAPPlugin = gsap.Plugin;
type GSAPPluginScope = gsap.PluginScope;
type GSAPPluginStatic = gsap.PluginStatic;
type GSAPStaggerVars = gsap.StaggerVars;
type GSAPTickerCallback = gsap.TickerCallback;
type GSAPTimeline = gsap.core.Timeline;
type GSAPTimelineVars = gsap.TimelineVars;
type GSAPTween = gsap.core.Tween;
type GSAPTweenTarget = gsap.TweenTarget;
type GSAPTweenVars = gsap.TweenVars;

type GSAP = typeof gsap;

declare module "gsap" {
  export * from "gsap/gsap-core";
  export { gsap as default } from "gsap/gsap-core";
}

declare module "gsap/src" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap/src/index" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap/dist" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap/dist/gsap" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap/all" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap/src/all" {
  export * from "gsap/all";
  export { gsap as default } from "gsap/all";
}

declare module "gsap-trial" {
  export * from "gsap/gsap-core";
  export { gsap as default } from "gsap/gsap-core";
}

declare module "gsap-trial/src" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap-trial/src/index" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap-trial/dist" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap-trial/dist/gsap" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap-trial/all" {
  export * from "gsap";
  export { gsap as default } from "gsap";
}

declare module "gsap-trial/src/all" {
  export * from "gsap/all";
  export { gsap as default } from "gsap/all";
}
