var Sf=Object.defineProperty,Mf=Object.defineProperties;var _f=Object.getOwnPropertyDescriptors;var Na=Object.getOwnPropertySymbols;var Tf=Object.prototype.hasOwnProperty,xf=Object.prototype.propertyIsEnumerable;var Ra=(e,t,n)=>t in e?Sf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,v=(e,t)=>{for(var n in t||={})Tf.call(t,n)&&Ra(e,n,t[n]);if(Na)for(var n of Na(t))xf.call(t,n)&&Ra(e,n,t[n]);return e},H=(e,t)=>Mf(e,_f(t));function Oa(e,t){return Object.is(e,t)}var q=null,tr=!1,Yo=1,bt=Symbol("SIGNAL");function L(e){let t=q;return q=e,t}function Ko(){return q}var nr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Jo(e){if(tr)throw new Error("");if(q===null)return;q.consumerOnSignalRead(e);let t=q.nextProducerIndex++;if(or(q),t<q.producerNode.length&&q.producerNode[t]!==e&&an(q)){let n=q.producerNode[t];rr(n,q.producerIndexOfThis[t])}q.producerNode[t]!==e&&(q.producerNode[t]=e,q.producerIndexOfThis[t]=an(q)?Fa(e,q,t):0),q.producerLastReadVersion[t]=e.version}function Aa(){Yo++}function ka(e){if(!(an(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Yo)){if(!e.producerMustRecompute(e)&&!ti(e)){Qo(e);return}e.producerRecomputeValue(e),Qo(e)}}function Xo(e){if(e.liveConsumerNode===void 0)return;let t=tr;tr=!0;try{for(let n of e.liveConsumerNode)n.dirty||Nf(n)}finally{tr=t}}function Pa(){return q?.consumerAllowSignalWrites!==!1}function Nf(e){e.dirty=!0,Xo(e),e.consumerMarkedDirty?.(e)}function Qo(e){e.dirty=!1,e.lastCleanEpoch=Yo}function ei(e){return e&&(e.nextProducerIndex=0),L(e)}function La(e,t){if(L(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(an(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)rr(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ti(e){or(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ka(n),r!==n.version))return!0}return!1}function ni(e){if(or(e),an(e))for(let t=0;t<e.producerNode.length;t++)rr(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Fa(e,t,n){if(ja(e),e.liveConsumerNode.length===0&&Va(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Fa(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function rr(e,t){if(ja(e),e.liveConsumerNode.length===1&&Va(e))for(let r=0;r<e.producerNode.length;r++)rr(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];or(o),o.producerIndexOfThis[r]=t}}function an(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function or(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ja(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Va(e){return e.producerNode!==void 0}function Rf(){throw new Error}var Ha=Rf;function Of(e){Ha(e)}function ri(e){Ha=e}var Af=null;function oi(e,t){Pa()||Of(e),e.equal(e.value,t)||(e.value=t,kf(e))}var ii=H(v({},nr),{equal:Oa,value:void 0,kind:"signal"});function kf(e){e.version++,Aa(),Xo(e),Af?.()}var si;function cn(){return si}function ke(e){let t=si;return si=e,t}var ir=Symbol("NotFound");function b(e){return typeof e=="function"}function Ct(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var sr=Ct(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function ln(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var $=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(b(r))try{r()}catch(i){t=i instanceof sr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ua(i)}catch(s){t=t??[],s instanceof sr?t=[...t,...s.errors]:t.push(s)}}if(t)throw new sr(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Ua(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&ln(n,t)}remove(t){let{_finalizers:n}=this;n&&ln(n,t),t instanceof e&&t._removeParent(this)}};$.EMPTY=(()=>{let e=new $;return e.closed=!0,e})();var ai=$.EMPTY;function ar(e){return e instanceof $||e&&"closed"in e&&b(e.remove)&&b(e.add)&&b(e.unsubscribe)}function Ua(e){b(e)?e():e.unsubscribe()}var Ie={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Dt={setTimeout(e,t,...n){let{delegate:r}=Dt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Dt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function cr(e){Dt.setTimeout(()=>{let{onUnhandledError:t}=Ie;if(t)t(e);else throw e})}function un(){}var $a=ci("C",void 0,void 0);function Ba(e){return ci("E",void 0,e)}function za(e){return ci("N",e,void 0)}function ci(e,t,n){return{kind:e,value:t,error:n}}var st=null;function St(e){if(Ie.useDeprecatedSynchronousErrorHandling){let t=!st;if(t&&(st={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=st;if(st=null,n)throw r}}else e()}function qa(e){Ie.useDeprecatedSynchronousErrorHandling&&st&&(st.errorThrown=!0,st.error=e)}var at=class extends ${constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ar(t)&&t.add(this)):this.destination=zf}static create(t,n,r){return new Mt(t,n,r)}next(t){this.isStopped?ui(za(t),this):this._next(t)}error(t){this.isStopped?ui(Ba(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?ui($a,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},$f=Function.prototype.bind;function li(e,t){return $f.call(e,t)}var di=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){lr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){lr(r)}else lr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){lr(n)}}},Mt=class extends at{constructor(t,n,r){super();let o;if(b(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ie.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&li(t.next,i),error:t.error&&li(t.error,i),complete:t.complete&&li(t.complete,i)}):o=t}this.destination=new di(o)}};function lr(e){Ie.useDeprecatedSynchronousErrorHandling?qa(e):cr(e)}function Bf(e){throw e}function ui(e,t){let{onStoppedNotification:n}=Ie;n&&Dt.setTimeout(()=>n(e,t))}var zf={closed:!0,next:un,error:Bf,complete:un};var _t=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ae(e){return e}function fi(...e){return pi(e)}function pi(e){return e.length===0?ae:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Wf(n)?n:new Mt(n,r,o);return St(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Wa(r),new r((o,i)=>{let s=new Mt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[_t](){return this}pipe(...n){return pi(n)(this)}toPromise(n){return n=Wa(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Wa(e){var t;return(t=e??Ie.Promise)!==null&&t!==void 0?t:Promise}function qf(e){return e&&b(e.next)&&b(e.error)&&b(e.complete)}function Wf(e){return e&&e instanceof at||qf(e)&&ar(e)}function hi(e){return b(e?.lift)}function N(e){return t=>{if(hi(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function R(e,t,n,r,o){return new gi(e,t,n,r,o)}var gi=class extends at{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Tt(){return N((e,t)=>{let n=null;e._refCount++;let r=R(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var xt=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,hi(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new $;let n=this.getSubject();t.add(this.source.subscribe(R(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=$.EMPTY)}return t}refCount(){return Tt()(this)}};var Ga=Ct(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Q=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new ur(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Ga}next(n){St(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){St(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){St(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ai:(this.currentObservers=null,i.push(n),new $(()=>{this.currentObservers=null,ln(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new ur(t,n),e})(),ur=class extends Q{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ai}};var Y=class extends Q{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var re=new F(e=>e.complete());function Za(e){return e&&b(e.schedule)}function Qa(e){return e[e.length-1]}function Ya(e){return b(Qa(e))?e.pop():void 0}function qe(e){return Za(Qa(e))?e.pop():void 0}function Ja(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Ka(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ct(e){return this instanceof ct?(this.v=e,this):new ct(e)}function Xa(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(y){return Promise.resolve(y).then(d,f)}}function a(d,y){r[d]&&(o[d]=function(x){return new Promise(function(X,ne){i.push([d,x,X,ne])>1||c(d,x)})},y&&(o[d]=y(o[d])))}function c(d,y){try{l(r[d](y))}catch(x){m(i[0][3],x)}}function l(d){d.value instanceof ct?Promise.resolve(d.value.v).then(u,f):m(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function m(d,y){d(y),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ec(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Ka=="function"?Ka(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var dr=e=>e&&typeof e.length=="number"&&typeof e!="function";function fr(e){return b(e?.then)}function pr(e){return b(e[_t])}function hr(e){return Symbol.asyncIterator&&b(e?.[Symbol.asyncIterator])}function gr(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Gf(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var mr=Gf();function vr(e){return b(e?.[mr])}function yr(e){return Xa(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield ct(n.read());if(o)return yield ct(void 0);yield yield ct(r)}}finally{n.releaseLock()}})}function Ir(e){return b(e?.getReader)}function G(e){if(e instanceof F)return e;if(e!=null){if(pr(e))return Zf(e);if(dr(e))return Qf(e);if(fr(e))return Yf(e);if(hr(e))return tc(e);if(vr(e))return Kf(e);if(Ir(e))return Jf(e)}throw gr(e)}function Zf(e){return new F(t=>{let n=e[_t]();if(b(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Qf(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Yf(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,cr)})}function Kf(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function tc(e){return new F(t=>{Xf(e,t).catch(n=>t.error(n))})}function Jf(e){return tc(yr(e))}function Xf(e,t){var n,r,o,i;return Ja(this,void 0,void 0,function*(){try{for(n=ec(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function oe(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function wr(e,t=0){return N((n,r)=>{n.subscribe(R(r,o=>oe(r,e,()=>r.next(o),t),()=>oe(r,e,()=>r.complete(),t),o=>oe(r,e,()=>r.error(o),t)))})}function Er(e,t=0){return N((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function nc(e,t){return G(e).pipe(Er(t),wr(t))}function rc(e,t){return G(e).pipe(Er(t),wr(t))}function oc(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ic(e,t){return new F(n=>{let r;return oe(n,t,()=>{r=e[mr](),oe(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>b(r?.return)&&r.return()})}function br(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{oe(n,t,()=>{let r=e[Symbol.asyncIterator]();oe(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function sc(e,t){return br(yr(e),t)}function ac(e,t){if(e!=null){if(pr(e))return nc(e,t);if(dr(e))return oc(e,t);if(fr(e))return rc(e,t);if(hr(e))return br(e,t);if(vr(e))return ic(e,t);if(Ir(e))return sc(e,t)}throw gr(e)}function B(e,t){return t?ac(e,t):G(e)}function C(...e){let t=qe(e);return B(e,t)}function Nt(e,t){let n=b(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function mi(e){return!!e&&(e instanceof F||b(e.lift)&&b(e.subscribe))}var Pe=Ct(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function O(e,t){return N((n,r)=>{let o=0;n.subscribe(R(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:ep}=Array;function tp(e,t){return ep(t)?e(...t):e(t)}function cc(e){return O(t=>tp(e,t))}var{isArray:np}=Array,{getPrototypeOf:rp,prototype:op,keys:ip}=Object;function lc(e){if(e.length===1){let t=e[0];if(np(t))return{args:t,keys:null};if(sp(t)){let n=ip(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function sp(e){return e&&typeof e=="object"&&rp(e)===op}function uc(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Cr(...e){let t=qe(e),n=Ya(e),{args:r,keys:o}=lc(e);if(r.length===0)return B([],t);let i=new F(ap(r,t,o?s=>uc(o,s):ae));return n?i.pipe(cc(n)):i}function ap(e,t,n=ae){return r=>{dc(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)dc(t,()=>{let l=B(e[c],t),u=!1;l.subscribe(R(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function dc(e,t,n){e?oe(n,e,t):t()}function fc(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,m=()=>{f&&!c.length&&!l&&t.complete()},d=x=>l<r?y(x):c.push(x),y=x=>{i&&t.next(x),l++;let X=!1;G(n(x,u++)).subscribe(R(t,ne=>{o?.(ne),i?d(ne):t.next(ne)},()=>{X=!0},void 0,()=>{if(X)try{for(l--;c.length&&l<r;){let ne=c.shift();s?oe(t,s,()=>y(ne)):y(ne)}m()}catch(ne){t.error(ne)}}))};return e.subscribe(R(t,d,()=>{f=!0,m()})),()=>{a?.()}}function W(e,t,n=1/0){return b(t)?W((r,o)=>O((i,s)=>t(r,i,o,s))(G(e(r,o))),n):(typeof t=="number"&&(n=t),N((r,o)=>fc(r,o,e,n)))}function pc(e=1/0){return W(ae,e)}function hc(){return pc(1)}function Rt(...e){return hc()(B(e,qe(e)))}function Dr(e){return new F(t=>{G(e()).subscribe(t)})}function we(e,t){return N((n,r)=>{let o=0;n.subscribe(R(r,i=>e.call(t,i,o++)&&r.next(i)))})}function We(e){return N((t,n)=>{let r=null,o=!1,i;r=t.subscribe(R(n,void 0,void 0,s=>{i=G(e(s,We(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function gc(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(R(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ot(e,t){return b(t)?W(e,t,1):W(e,1)}function Ge(e){return N((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Le(e){return e<=0?()=>re:N((t,n)=>{let r=0;t.subscribe(R(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Sr(e=cp){return N((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function cp(){return new Pe}function dn(e){return N((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Fe(e,t){let n=arguments.length>=2;return r=>r.pipe(e?we((o,i)=>e(o,i,r)):ae,Le(1),n?Ge(t):Sr(()=>new Pe))}function At(e){return e<=0?()=>re:N((t,n)=>{let r=[];t.subscribe(R(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function vi(e,t){let n=arguments.length>=2;return r=>r.pipe(e?we((o,i)=>e(o,i,r)):ae,At(1),n?Ge(t):Sr(()=>new Pe))}function yi(e,t){return N(gc(e,t,arguments.length>=2,!0))}function Ii(...e){let t=qe(e);return N((n,r)=>{(t?Rt(e,n,t):Rt(e,n)).subscribe(r)})}function Ee(e,t){return N((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(R(r,c=>{o?.unsubscribe();let l=0,u=i++;G(e(c,u)).subscribe(o=R(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function wi(e){return N((t,n)=>{G(e).subscribe(R(n,()=>n.complete(),un)),!n.closed&&t.subscribe(n)})}function K(e,t,n){let r=b(e)||t||n?{next:e,error:t,complete:n}:e;return r?N((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(R(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):ae}var I=class extends Error{code;constructor(t,n){super(dp(t,n)),this.code=t}};function up(e){return`NG0${Math.abs(e)}`}function dp(e,t){return`${up(e)}${t?": "+t:""}`}var Xc=Symbol("InputSignalNode#UNSET"),fp=H(v({},ii),{transformFn:void 0,applyValueToInputSignal(e,t){oi(e,t)}});function el(e,t){let n=Object.create(fp);n.value=e,n.transformFn=t?.transform;function r(){if(Jo(n),n.value===Xc){let o=null;throw new I(-950,o)}return n.value}return r[bt]=n,r}function as(e){return{toString:e}.toString()}function j(e){for(let t in e)if(e[t]===j)return t;throw Error("Could not find renamed property on target object.")}function ce(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ce).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function mc(e,t){return e?t?`${e} ${t}`:e:t||""}var pp=j({__forward_ref__:j});function tl(e){return e.__forward_ref__=tl,e.toString=function(){return ce(this())},e}function pe(e){return nl(e)?e():e}function nl(e){return typeof e=="function"&&e.hasOwnProperty(pp)&&e.__forward_ref__===tl}function E(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Kr(e){return vc(e,ol)||vc(e,il)}function rl(e){return Kr(e)!==null}function vc(e,t){return e.hasOwnProperty(t)?e[t]:null}function hp(e){let t=e&&(e[ol]||e[il]);return t||null}function yc(e){return e&&(e.hasOwnProperty(Ic)||e.hasOwnProperty(gp))?e[Ic]:null}var ol=j({\u0275prov:j}),Ic=j({\u0275inj:j}),il=j({ngInjectableDef:j}),gp=j({ngInjectorDef:j}),w=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=E({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function sl(e){return e&&!!e.\u0275providers}var mp=j({\u0275cmp:j}),vp=j({\u0275dir:j}),yp=j({\u0275pipe:j}),Ip=j({\u0275mod:j}),Rr=j({\u0275fac:j}),gn=j({__NG_ELEMENT_ID__:j}),wc=j({__NG_ENV_ID__:j});function al(e){return typeof e=="string"?e:e==null?"":String(e)}function wp(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():al(e)}function cl(e,t){throw new I(-200,e)}function cs(e,t){throw new I(-201,!1)}var S=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(S||{}),xi;function ll(){return xi}function fe(e){let t=xi;return xi=e,t}function ul(e,t,n){let r=Kr(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&S.Optional)return null;if(t!==void 0)return t;cs(e,"Injector")}var Ep={},lt=Ep,bp="__NG_DI_FLAG__",Or=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?ir:lt,r)}},Ar="ngTempTokenPath",Cp="ngTokenPath",Dp=/\n/gm,Sp="\u0275",Ec="__source";function Mp(e,t=S.Default){if(cn()===void 0)throw new I(-203,!1);if(cn()===null)return ul(e,void 0,t);{let n=cn(),r;return n instanceof Or?r=n.injector:r=n,r.get(e,t&S.Optional?null:void 0,t)}}function T(e,t=S.Default){return(ll()||Mp)(pe(e),t)}function p(e,t=S.Default){return T(e,Jr(t))}function Jr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ni(e){let t=[];for(let n=0;n<e.length;n++){let r=pe(e[n]);if(Array.isArray(r)){if(r.length===0)throw new I(900,!1);let o,i=S.Default;for(let s=0;s<r.length;s++){let a=r[s],c=_p(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(T(o,i))}else t.push(T(r))}return t}function _p(e){return e[bp]}function Tp(e,t,n,r){let o=e[Ar];throw t[Ec]&&o.unshift(t[Ec]),e.message=xp(`
`+e.message,o,n,r),e[Cp]=o,e[Ar]=null,e}function xp(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Sp?e.slice(2):e;let o=ce(t);if(Array.isArray(t))o=t.map(ce).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ce(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Dp,`
  `)}`}function jt(e,t){let n=e.hasOwnProperty(Rr);return n?e[Rr]:null}function ls(e,t){e.forEach(n=>Array.isArray(n)?ls(n,t):t(n))}function dl(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function kr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}var Vt={},Ht=[],mn=new w(""),fl=new w("",-1),pl=new w(""),Pr=class{get(t,n=lt){if(n===lt){let r=new Error(`NullInjectorError: No provider for ${ce(t)}!`);throw r.name="NullInjectorError",r}return n}};function hl(e,t){let n=e[Ip]||null;if(!n&&t===!0)throw new Error(`Type ${ce(e)} does not have '\u0275mod' property.`);return n}function Ut(e){return e[mp]||null}function Np(e){return e[vp]||null}function Rp(e){return e[yp]||null}function us(e){return{\u0275providers:e}}function Op(...e){return{\u0275providers:gl(!0,e),\u0275fromNgModule:!0}}function gl(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ls(t,s=>{let a=s;Ri(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ml(o,i),n}function ml(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ds(o,i=>{t(i,r)})}}function Ri(e,t,n,r){if(e=pe(e),!e)return!1;let o=null,i=yc(e),s=!i&&Ut(e);if(!i&&!s){let c=e.ngModule;if(i=yc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Ri(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{ls(i.imports,u=>{Ri(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&ml(l,t)}if(!a){let l=jt(o)||(()=>new o);t({provide:o,useFactory:l,deps:Ht},o),t({provide:pl,useValue:o,multi:!0},o),t({provide:mn,useValue:()=>T(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ds(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ds(e,t){for(let n of e)sl(n)&&(n=n.\u0275providers),Array.isArray(n)?ds(n,t):t(n)}var Ap=j({provide:String,useValue:j});function vl(e){return e!==null&&typeof e=="object"&&Ap in e}function kp(e){return!!(e&&e.useExisting)}function Pp(e){return!!(e&&e.useFactory)}function Oi(e){return typeof e=="function"}var Xr=new w(""),Mr={},bc={},Ei;function fs(){return Ei===void 0&&(Ei=new Pr),Ei}var me=class{},vn=class extends me{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,ki(t,s=>this.processProvider(s)),this.records.set(fl,kt(void 0,this)),o.has("environment")&&this.records.set(me,kt(void 0,this));let i=this.records.get(Xr);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(pl,Ht,S.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?ir:lt,r)}destroy(){pn(this),this._destroyed=!0;let t=L(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),L(t)}}onDestroy(t){return pn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){pn(this);let n=ke(this),r=fe(void 0),o;try{return t()}finally{ke(n),fe(r)}}get(t,n=lt,r=S.Default){if(pn(this),t.hasOwnProperty(wc))return t[wc](this);r=Jr(r);let o,i=ke(this),s=fe(void 0);try{if(!(r&S.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=Up(t)&&Kr(t);l&&this.injectableDefInScope(l)?c=kt(Ai(t),Mr):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&S.Self?fs():this.parent;return n=r&S.Optional&&n===lt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Ar]=a[Ar]||[]).unshift(ce(t)),i)throw a;return Tp(a,t,"R3InjectorError",this.source)}else throw a}finally{fe(s),ke(i)}}resolveInjectorInitializers(){let t=L(null),n=ke(this),r=fe(void 0),o;try{let i=this.get(mn,Ht,S.Self);for(let s of i)s()}finally{ke(n),fe(r),L(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ce(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=pe(t);let n=Oi(t)?t:pe(t&&t.provide),r=Fp(t);if(!Oi(t)&&t.multi===!0){let o=this.records.get(n);o||(o=kt(void 0,Mr,!0),o.factory=()=>Ni(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=L(null);try{return n.value===bc?cl(ce(t)):n.value===Mr&&(n.value=bc,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Hp(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{L(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=pe(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Ai(e){let t=Kr(e),n=t!==null?t.factory:jt(e);if(n!==null)return n;if(e instanceof w)throw new I(204,!1);if(e instanceof Function)return Lp(e);throw new I(204,!1)}function Lp(e){if(e.length>0)throw new I(204,!1);let n=hp(e);return n!==null?()=>n.factory(e):()=>new e}function Fp(e){if(vl(e))return kt(void 0,e.useValue);{let t=jp(e);return kt(t,Mr)}}function jp(e,t,n){let r;if(Oi(e)){let o=pe(e);return jt(o)||Ai(o)}else if(vl(e))r=()=>pe(e.useValue);else if(Pp(e))r=()=>e.useFactory(...Ni(e.deps||[]));else if(kp(e))r=(o,i)=>T(pe(e.useExisting),i!==void 0&&i&S.Optional?S.Optional:void 0);else{let o=pe(e&&(e.useClass||e.provide));if(Vp(e))r=()=>new o(...Ni(e.deps));else return jt(o)||Ai(o)}return r}function pn(e){if(e.destroyed)throw new I(205,!1)}function kt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Vp(e){return!!e.deps}function Hp(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Up(e){return typeof e=="function"||typeof e=="object"&&e instanceof w}function ki(e,t){for(let n of e)Array.isArray(n)?ki(n,t):n&&sl(n)?ki(n.\u0275providers,t):t(n)}function Se(e,t){let n;e instanceof vn?(pn(e),n=e):n=new Or(e);let r,o=ke(n),i=fe(void 0);try{return t()}finally{ke(o),fe(i)}}function $p(){return ll()!==void 0||cn()!=null}function Bp(e){return typeof e=="function"}var Ve=0,A=1,M=2,te=3,Ce=4,Me=5,Lr=6,Cc=7,De=8,yn=9,Qe=10,ve=11,In=12,Dc=13,Sn=14,xe=15,wn=16,Pt=17,eo=18,to=19,yl=20,Ze=21,bi=22,Fr=23,he=24,Ci=25,Ye=26,Il=1;var ft=7,jr=8,Vr=9,ge=10;function ut(e){return Array.isArray(e)&&typeof e[Il]=="object"}function He(e){return Array.isArray(e)&&e[Il]===!0}function wl(e){return(e.flags&4)!==0}function Mn(e){return e.componentOffset>-1}function zp(e){return(e.flags&1)===1}function mt(e){return!!e.template}function Hr(e){return(e[M]&512)!==0}function Bt(e){return(e[M]&256)===256}var Pi=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function El(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var bl=(()=>{let e=()=>Cl;return e.ngInherit=!0,e})();function Cl(e){return e.type.prototype.ngOnChanges&&(e.setInput=Wp),qp}function qp(){let e=Sl(this),t=e?.current;if(t){let n=e.previous;if(n===Vt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Wp(e,t,n,r,o){let i=this.declaredInputs[r],s=Sl(e)||Gp(e,{previous:Vt,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Pi(l&&l.currentValue,n,c===Vt),El(e,t,o,n)}var Dl="__ngSimpleChanges__";function Sl(e){return e[Dl]||null}function Gp(e,t){return e[Dl]=t}var Sc=null;var U=function(e,t=null,n){Sc?.(e,t,n)},Zp="svg",Qp="math";function je(e){for(;Array.isArray(e);)e=e[Ve];return e}function Yp(e,t){return je(t[e])}function et(e,t){return je(t[e.index])}function Ml(e,t){return e.data[t]}function Ke(e,t){let n=t[e];return ut(n)?n:n[Ve]}function ps(e){return(e[M]&128)===128}function Kp(e){return He(e[te])}function Mc(e,t){return t==null?null:e[t]}function _l(e){e[Pt]=0}function Tl(e){e[M]&1024||(e[M]|=1024,ps(e)&&ro(e))}function no(e){return!!(e[M]&9216||e[he]?.dirty)}function Li(e){e[Qe].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),no(e)&&ro(e)}function ro(e){e[Qe].changeDetectionScheduler?.notify(0);let t=pt(e);for(;t!==null&&!(t[M]&8192||(t[M]|=8192,!ps(t)));)t=pt(t)}function xl(e,t){if(Bt(e))throw new I(911,!1);e[Ze]===null&&(e[Ze]=[]),e[Ze].push(t)}function Jp(e,t){if(e[Ze]===null)return;let n=e[Ze].indexOf(t);n!==-1&&e[Ze].splice(n,1)}function pt(e){let t=e[te];return He(t)?t[te]:t}var k={lFrame:Fl(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Fi=!1;function Xp(){return k.lFrame.elementDepthCount}function eh(){k.lFrame.elementDepthCount++}function th(){k.lFrame.elementDepthCount--}function nh(){return k.bindingsEnabled}function rh(){return k.skipHydrationRootTNode!==null}function oh(e){return k.skipHydrationRootTNode===e}function ih(){k.skipHydrationRootTNode=null}function le(){return k.lFrame.lView}function _n(){return k.lFrame.tView}function vt(){let e=Nl();for(;e!==null&&e.type===64;)e=e.parent;return e}function Nl(){return k.lFrame.currentTNode}function sh(){let e=k.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function oo(e,t){let n=k.lFrame;n.currentTNode=e,n.isParent=t}function Rl(){return k.lFrame.isParent}function ah(){k.lFrame.isParent=!1}function Ol(){return Fi}function _c(e){let t=Fi;return Fi=e,t}function ch(e){return k.lFrame.bindingIndex=e}function Al(){return k.lFrame.bindingIndex++}function lh(){return k.lFrame.inI18n}function uh(e,t){let n=k.lFrame;n.bindingIndex=n.bindingRootIndex=e,ji(t)}function dh(){return k.lFrame.currentDirectiveIndex}function ji(e){k.lFrame.currentDirectiveIndex=e}function kl(e){k.lFrame.currentQueryIndex=e}function fh(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Me]:null}function Pl(e,t,n){if(n&S.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&S.Host);)if(o=fh(i),o===null||(i=i[Sn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=k.lFrame=Ll();return r.currentTNode=t,r.lView=e,!0}function hs(e){let t=Ll(),n=e[A];k.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ll(){let e=k.lFrame,t=e===null?null:e.child;return t===null?Fl(e):t}function Fl(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function jl(){let e=k.lFrame;return k.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Vl=jl;function gs(){let e=jl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function ms(){return k.lFrame.selectedIndex}function ht(e){k.lFrame.selectedIndex=e}function ph(){let e=k.lFrame;return Ml(e.tView,e.selectedIndex)}function hh(){return k.lFrame.currentNamespace}var Hl=!0;function Ul(){return Hl}function $l(e){Hl=e}function gh(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Cl(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function mh(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function _r(e,t,n){Bl(e,t,3,n)}function Tr(e,t,n,r){(e[M]&3)===n&&Bl(e,t,n,r)}function Di(e,t){let n=e[M];(n&3)===t&&(n&=16383,n+=1,e[M]=n)}function Bl(e,t,n,r){let o=r!==void 0?e[Pt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Pt]+=65536),(a<i||i==-1)&&(vh(e,n,t,c),e[Pt]=(e[Pt]&**********)+c+2),c++}function Tc(e,t){U(4,e,t);let n=L(null);try{t.call(e)}finally{L(n),U(5,e,t)}}function vh(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[M]>>14<e[Pt]>>16&&(e[M]&3)===t&&(e[M]+=16384,Tc(a,i)):Tc(a,i)}var Ft=-1,En=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function yh(e){return(e.flags&8)!==0}function Ih(e){return(e.flags&16)!==0}function wh(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];bh(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Eh(e){return e===3||e===4||e===6}function bh(e){return e.charCodeAt(0)===64}function zl(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?xc(e,n,o,null,t[++r]):xc(e,n,o,null,null))}}return e}function xc(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function ql(e){return e!==Ft}function Ur(e){return e&32767}function Ch(e){return e>>16}function $r(e,t){let n=Ch(e),r=t;for(;n>0;)r=r[Sn],n--;return r}var Vi=!0;function Nc(e){let t=Vi;return Vi=e,t}var Dh=256,Wl=Dh-1,Gl=5,Sh=0,Te={};function Mh(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(gn)&&(r=n[gn]),r==null&&(r=n[gn]=Sh++);let o=r&Wl,i=1<<o;t.data[e+(o>>Gl)]|=i}function Zl(e,t){let n=Ql(e,t);if(n!==-1)return n;let r=t[A];r.firstCreatePass&&(e.injectorIndex=t.length,Si(r.data,e),Si(t,null),Si(r.blueprint,null));let o=vs(e,t),i=e.injectorIndex;if(ql(o)){let s=Ur(o),a=$r(o,t),c=a[A].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Si(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Ql(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function vs(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=eu(o),r===null)return Ft;if(n++,o=o[Sn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Ft}function _h(e,t,n){Mh(e,t,n)}function Yl(e,t,n){if(n&S.Optional||e!==void 0)return e;cs(t,"NodeInjector")}function Kl(e,t,n,r){if(n&S.Optional&&r===void 0&&(r=null),(n&(S.Self|S.Host))===0){let o=e[yn],i=fe(void 0);try{return o?o.get(t,r,n&S.Optional):ul(t,r,n&S.Optional)}finally{fe(i)}}return Yl(r,t,n)}function Jl(e,t,n,r=S.Default,o){if(e!==null){if(t[M]&2048&&!(r&S.Self)){let s=Oh(e,t,n,r,Te);if(s!==Te)return s}let i=Xl(e,t,n,r,Te);if(i!==Te)return i}return Kl(t,n,r,o)}function Xl(e,t,n,r,o){let i=Nh(n);if(typeof i=="function"){if(!Pl(t,e,r))return r&S.Host?Yl(o,n,r):Kl(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&S.Optional))cs(n);else return s}finally{Vl()}}else if(typeof i=="number"){let s=null,a=Ql(e,t),c=Ft,l=r&S.Host?t[xe][Me]:null;for((a===-1||r&S.SkipSelf)&&(c=a===-1?vs(e,t):t[a+8],c===Ft||!Oc(r,!1)?a=-1:(s=t[A],a=Ur(c),t=$r(c,t)));a!==-1;){let u=t[A];if(Rc(i,a,u.data)){let f=Th(a,t,n,s,r,l);if(f!==Te)return f}c=t[a+8],c!==Ft&&Oc(r,t[A].data[a+8]===l)&&Rc(i,a,t)?(s=u,a=Ur(c),t=$r(c,t)):a=-1}}return o}function Th(e,t,n,r,o,i){let s=t[A],a=s.data[e+8],c=r==null?Mn(a)&&Vi:r!=s&&(a.type&3)!==0,l=o&S.Host&&i===a,u=xh(a,s,n,c,l);return u!==null?Hi(t,s,u,a,o):Te}function xh(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,m=o?a+u:l;for(let d=f;d<m;d++){let y=s[d];if(d<c&&n===y||d>=c&&y.type===n)return d}if(o){let d=s[c];if(d&&mt(d)&&d.type===n)return c}return null}function Hi(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof En){let a=i;a.resolving&&cl(wp(s[n]));let c=Nc(a.canSeeViewProviders);a.resolving=!0;let l,u=a.injectImpl?fe(a.injectImpl):null,f=Pl(e,r,S.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&gh(n,s[n],t)}finally{u!==null&&fe(u),Nc(c),a.resolving=!1,Vl()}}return i}function Nh(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(gn)?e[gn]:void 0;return typeof t=="number"?t>=0?t&Wl:Rh:t}function Rc(e,t,n){let r=1<<e;return!!(n[t+(e>>Gl)]&r)}function Oc(e,t){return!(e&S.Self)&&!(e&S.Host&&t)}var dt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Jl(this._tNode,this._lView,t,Jr(r),n)}};function Rh(){return new dt(vt(),le())}function ys(e){return as(()=>{let t=e.prototype.constructor,n=t[Rr]||Ui(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Rr]||Ui(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ui(e){return nl(e)?()=>{let t=Ui(pe(e));return t&&t()}:jt(e)}function Oh(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[M]&2048&&!Hr(s);){let a=Xl(i,s,n,r|S.Self,Te);if(a!==Te)return a;let c=i.parent;if(!c){let l=s[yl];if(l){let u=l.get(n,Te,r);if(u!==Te)return u}c=eu(s),s=s[Sn]}i=c}return o}function eu(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Me]:null}function Ac(e,t=null,n=null,r){let o=tu(e,t,n,r);return o.resolveInjectorInitializers(),o}function tu(e,t=null,n=null,r,o=new Set){let i=[n||Ht,Op(e)];return r=r||(typeof e=="object"?void 0:ce(e)),new vn(i,t||fs(),r||null,o)}var Je=class e{static THROW_IF_NOT_FOUND=lt;static NULL=new Pr;static create(t,n){if(Array.isArray(t))return Ac({name:""},n,t,"");{let r=t.name??"";return Ac({name:r},t.parent,t.providers,r)}}static \u0275prov=E({token:e,providedIn:"any",factory:()=>T(fl)});static __NG_ELEMENT_ID__=-1};var Ah=new w("");Ah.__NG_ELEMENT_ID__=e=>{let t=vt();if(t===null)throw new I(204,!1);if(t.type&2)return t.value;if(e&S.Optional)return null;throw new I(204,!1)};var nu=!1,io=(()=>{class e{static __NG_ELEMENT_ID__=kh;static __NG_ENV_ID__=n=>n}return e})(),$i=class extends io{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Bt(n)?(t(),()=>{}):(xl(n,t),()=>Jp(n,t))}};function kh(){return new $i(le())}var bn=class{},Is=new w("",{providedIn:"root",factory:()=>!1});var ru=new w(""),ou=new w(""),zt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Y(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})();var Bi=class extends Q{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,$p()&&(this.destroyRef=p(io,{optional:!0})??void 0,this.pendingTasks=p(zt,{optional:!0})??void 0)}emit(t){let n=L(null);try{super.next(t)}finally{L(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof $&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ie=Bi;function Br(...e){}function iu(e){let t,n;function r(){e=Br;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function kc(e){return queueMicrotask(()=>e()),()=>{e=Br}}var ws="isAngularZone",zr=ws+"_ID",Ph=0,Z=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ie(!1);onMicrotaskEmpty=new ie(!1);onStable=new ie(!1);onError=new ie(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=nu}=t;if(typeof Zone>"u")throw new I(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,jh(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ws)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new I(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new I(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Lh,Br,Br);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Lh={};function Es(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Fh(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){iu(()=>{e.callbackScheduled=!1,zi(e),e.isCheckStableRunning=!0,Es(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),zi(e)}function jh(e){let t=()=>{Fh(e)},n=Ph++;e._inner=e._inner.fork({name:"angular",properties:{[ws]:!0,[zr]:n,[zr+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Vh(c))return r.invokeTask(i,s,a,c);try{return Pc(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Lc(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Pc(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Hh(c)&&t(),Lc(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,zi(e),Es(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function zi(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Pc(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Lc(e){e._nesting--,Es(e)}var qi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ie;onMicrotaskEmpty=new ie;onStable=new ie;onError=new ie;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Vh(e){return su(e,"__ignore_ng_zone__")}function Hh(e){return su(e,"__scheduler_tick__")}function su(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Xe=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Uh=new w("",{providedIn:"root",factory:()=>{let e=p(Z),t=p(Xe);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Fc(e,t){return el(e,t)}function $h(e){return el(Xc,e)}var au=(Fc.required=$h,Fc);function Bh(){return bs(vt(),le())}function bs(e,t){return new zh(et(e,t))}var zh=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Bh}return e})();function cu(e){return(e.flags&128)===128}var lu=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(lu||{}),uu=new Map,qh=0;function Wh(){return qh++}function Gh(e){uu.set(e[to],e)}function Wi(e){uu.delete(e[to])}var jc="__ngContext__";function so(e,t){ut(t)?(e[jc]=t[to],Gh(t)):e[jc]=t}function du(e){return pu(e[In])}function fu(e){return pu(e[Ce])}function pu(e){for(;e!==null&&!He(e);)e=e[Ce];return e}var Gi;function hu(e){Gi=e}function Zh(){if(Gi!==void 0)return Gi;if(typeof document<"u")return document;throw new I(210,!1)}var Cs=new w("",{providedIn:"root",factory:()=>Qh}),Qh="ng",Ds=new w(""),Tn=new w("",{providedIn:"platform",factory:()=>"unknown"});var Ss=new w("",{providedIn:"root",factory:()=>Zh().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Yh="h",Kh="b";var gu=!1,Jh=new w("",{providedIn:"root",factory:()=>gu});var mu=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(mu||{}),ao=new w(""),Vc=new Set;function Ms(e){Vc.has(e)||(Vc.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Xh=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})();var eg=()=>null;function vu(e,t,n=!1){return eg(e,t,n)}function yu(e,t){let n=e.contentQueries;if(n!==null){let r=L(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];kl(i),a.contentQueries(2,t[s],s)}}}finally{L(r)}}}function Zi(e,t,n){kl(0);let r=L(null);try{t(e,n)}finally{L(r)}}function Iu(e,t,n){if(wl(t)){let r=L(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{L(r)}}}var Ne=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ne||{});function wu(e){return e instanceof Function?e():e}function tg(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Eu="ng-template";function ng(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&tg(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(_s(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function _s(e){return e.type===4&&e.value!==Eu}function rg(e,t,n){let r=e.type===4&&!n?Eu:e.value;return t===r}function og(e,t,n){let r=4,o=e.attrs,i=o!==null?ag(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!be(r)&&!be(c))return!1;if(s&&be(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!rg(e,c,n)||c===""&&t.length===1){if(be(r))return!1;s=!0}}else if(r&8){if(o===null||!ng(e,o,c,n)){if(be(r))return!1;s=!0}}else{let l=t[++a],u=ig(c,o,_s(e),n);if(u===-1){if(be(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(be(r))return!1;s=!0}}}}return be(r)||s}function be(e){return(e&1)===0}function ig(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return cg(t,e)}function sg(e,t,n=!1){for(let r=0;r<t.length;r++)if(og(e,t[r],n))return!0;return!1}function ag(e){for(let t=0;t<e.length;t++){let n=e[t];if(Eh(n))return t}return e.length}function cg(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Hc(e,t){return e?":not("+t.trim()+")":t}function lg(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!be(s)&&(t+=Hc(i,o),o=""),r=s,i=i||!be(r);n++}return o!==""&&(t+=Hc(i,o)),t}function ug(e){return e.map(lg).join(",")}function dg(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!be(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var co={};function fg(e,t){return e.createText(t)}function pg(e,t,n){e.setValue(t,n)}function bu(e,t,n){return e.createElement(t,n)}function qr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Cu(e,t,n){e.appendChild(t,n)}function Uc(e,t,n,r,o){r!==null?qr(e,t,n,r,o):Cu(e,t,n)}function hg(e,t,n){e.removeChild(null,t,n)}function gg(e,t,n){e.setAttribute(t,"style",n)}function mg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Du(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&wh(e,t,r),o!==null&&mg(e,t,o),i!==null&&gg(e,t,i)}function Su(e,t,n,r,o,i,s,a,c,l,u){let f=Ye+r,m=f+o,d=vg(f,m),y=typeof l=="function"?l():l;return d[A]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:m,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:y,incompleteFirstPass:!1,ssrId:u}}function vg(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:co);return n}function yg(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Su(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Mu(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[Ve]=o,f[M]=r|4|128|8|64|1024,(l!==null||e&&e[M]&2048)&&(f[M]|=2048),_l(f),f[te]=f[Sn]=e,f[De]=n,f[Qe]=s||e&&e[Qe],f[ve]=a||e&&e[ve],f[yn]=c||e&&e[yn]||null,f[Me]=i,f[to]=Wh(),f[Lr]=u,f[yl]=l,f[xe]=t.type==2?e[xe]:f,f}function Ig(e,t,n){let r=et(t,e),o=yg(n),i=e[Qe].rendererFactory,s=xu(e,Mu(e,o,null,_u(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function _u(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Tu(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function xu(e,t){return e[In]?e[Dc][Ce]=t:e[In]=t,e[Dc]=t,t}function Re(e=1){Nu(_n(),le(),ms()+e,!1)}function Nu(e,t,n,r){if(!r)if((t[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&_r(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Tr(t,i,0,n)}ht(n)}var lo=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(lo||{});function Qi(e,t,n,r){let o=L(null);try{let[i,s,a]=e.inputs[n],c=null;(s&lo.SignalBased)!==0&&(c=t[i][bt]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):El(t,c,i,r)}finally{L(o)}}function Ru(e,t,n,r,o){let i=ms(),s=r&2;try{ht(-1),s&&t.length>Ye&&Nu(e,t,Ye,!1),U(s?2:0,o),n(r,o)}finally{ht(i),U(s?3:1,o)}}function Ou(e,t,n){_g(e,t,n),(n.flags&64)===64&&Tg(e,t,n)}function wg(e,t,n=et){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Eg(e,t,n,r){let i=r.get(Jh,gu)||n===Ne.ShadowDom,s=e.selectRootElement(t,i);return bg(s),s}function bg(e){Cg(e)}var Cg=()=>null;function Dg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Sg(e,t,n,r,o,i,s,a){if(!a&&Ts(t,e,n,r,o)){Mn(t)&&Mg(n,t.index);return}if(t.type&3){let c=et(t,n);r=Dg(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function Mg(e,t){let n=Ke(t,e);n[M]&16||(n[M]|=64)}function _g(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Mn(n)&&Ig(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Zl(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Hi(t,e,s,n);if(so(c,t),i!==null&&Rg(t,s-r,c,a,n,i),mt(a)){let l=Ke(n.index,t);l[De]=Hi(t,e,s,n)}}}function Tg(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=dh();try{ht(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];ji(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&xg(c,l)}}finally{ht(-1),ji(s)}}function xg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Ng(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];sg(t,i.selectors,!1)&&(r??=[],mt(i)?r.unshift(i):r.push(i))}return r}function Rg(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Qi(r,n,c,l)}}function Og(e,t){let n=e[yn],r=n?n.get(Xe,null):null;r&&r.handleError(t)}function Ts(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];Qi(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];Qi(u,l,r,o),a=!0}return a}function Ag(e,t){let n=Ke(t,e),r=n[A];kg(r,n);let o=n[Ve];o!==null&&n[Lr]===null&&(n[Lr]=vu(o,n[yn])),U(18),Au(r,n,n[De]),U(19,n[De])}function kg(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Au(e,t,n){hs(t);try{let r=e.viewQuery;r!==null&&Zi(1,r,n);let o=e.template;o!==null&&Ru(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[eo]?.finishViewCreation(e),e.staticContentQueries&&yu(e,t),e.staticViewQueries&&Zi(2,e.viewQuery,n);let i=e.components;i!==null&&Pg(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[M]&=-5,gs()}}function Pg(e,t){for(let n=0;n<t.length;n++)Ag(e,t[n])}function $c(e,t){return!t||t.firstChild===null||cu(e)}var Lg;function xs(e,t){return Lg(e,t)}var qt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(qt||{});function ku(e){return(e.flags&32)===32}function Lt(e,t,n,r,o){if(r!=null){let i,s=!1;He(r)?i=r:ut(r)&&(s=!0,r=r[Ve]);let a=je(r);e===0&&n!==null?o==null?Cu(t,n,a):qr(t,n,a,o||null,!0):e===1&&n!==null?qr(t,n,a,o||null,!0):e===2?hg(t,a,s):e===3&&t.destroyNode(a),i!=null&&Zg(t,e,i,n,o)}}function Fg(e,t){Pu(e,t),t[Ve]=null,t[Me]=null}function jg(e,t,n,r,o,i){r[Ve]=o,r[Me]=t,uo(e,r,n,1,o,i)}function Pu(e,t){t[Qe].changeDetectionScheduler?.notify(9),uo(e,t,t[ve],2,null,null)}function Vg(e){let t=e[In];if(!t)return Mi(e[A],e);for(;t;){let n=null;if(ut(t))n=t[In];else{let r=t[ge];r&&(n=r)}if(!n){for(;t&&!t[Ce]&&t!==e;)ut(t)&&Mi(t[A],t),t=t[te];t===null&&(t=e),ut(t)&&Mi(t[A],t),n=t&&t[Ce]}t=n}}function Ns(e,t){let n=e[Vr],r=n.indexOf(t);n.splice(r,1)}function Lu(e,t){if(Bt(t))return;let n=t[ve];n.destroyNode&&uo(e,t,n,3,null,null),Vg(t)}function Mi(e,t){if(Bt(t))return;let n=L(null);try{t[M]&=-129,t[M]|=256,t[he]&&ni(t[he]),Ug(e,t),Hg(e,t),t[A].type===1&&t[ve].destroy();let r=t[wn];if(r!==null&&He(t[te])){r!==t[te]&&Ns(r,t);let o=t[eo];o!==null&&o.detachView(e)}Wi(t)}finally{L(n)}}function Hg(e,t){let n=e.cleanup,r=t[Cc];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Cc]=null);let o=t[Ze];if(o!==null){t[Ze]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Fr];if(i!==null){t[Fr]=null;for(let s of i)s.destroy()}}function Ug(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof En)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];U(4,a,c);try{c.call(a)}finally{U(5,a,c)}}else{U(4,o,i);try{i.call(o)}finally{U(5,o,i)}}}}}function $g(e,t,n){return Bg(e,t.parent,n)}function Bg(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Ve];if(Mn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ne.None||o===Ne.Emulated)return null}return et(r,n)}function zg(e,t,n){return Wg(e,t,n)}function qg(e,t,n){return e.type&40?et(e,n):null}var Wg=qg,Bc;function Fu(e,t,n,r){let o=$g(e,r,t),i=t[ve],s=r.parent||t[Me],a=zg(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Uc(i,o,n[c],a,!1);else Uc(i,o,n,a,!1);Bc!==void 0&&Bc(i,r,t,n,o)}function hn(e,t){if(t!==null){let n=t.type;if(n&3)return et(t,e);if(n&4)return Yi(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return hn(e,r);{let o=e[t.index];return He(o)?Yi(-1,o):je(o)}}else{if(n&128)return hn(e,t.next);if(n&32)return xs(t,e)()||je(e[t.index]);{let r=ju(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=pt(e[xe]);return hn(o,r)}else return hn(e,t.next)}}}return null}function ju(e,t){if(t!==null){let r=e[xe][Me],o=t.projection;return r.projection[o]}return null}function Yi(e,t){let n=ge+e+1;if(n<t.length){let r=t[n],o=r[A].firstChild;if(o!==null)return hn(r,o)}return t[ft]}function Rs(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&so(je(a),r),n.flags|=2),!ku(n))if(c&8)Rs(e,t,n.child,r,o,i,!1),Lt(t,e,o,a,i);else if(c&32){let l=xs(n,r),u;for(;u=l();)Lt(t,e,o,u,i);Lt(t,e,o,a,i)}else c&16?Gg(e,t,r,n,o,i):Lt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function uo(e,t,n,r,o,i){Rs(n,r,e.firstChild,t,o,i,!1)}function Gg(e,t,n,r,o,i){let s=n[xe],c=s[Me].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Lt(t,e,o,u,i)}else{let l=c,u=s[te];cu(r)&&(l.flags|=128),Rs(e,t,l,u,o,i,!0)}}function Zg(e,t,n,r,o){let i=n[ft],s=je(n);i!==s&&Lt(t,e,r,i,o);for(let a=ge;a<n.length;a++){let c=n[a];uo(c[A],c,e,t,r,i)}}function Wr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(je(i)),He(i)&&Qg(i,r);let s=n.type;if(s&8)Wr(e,t,n.child,r);else if(s&32){let a=xs(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=ju(t,n);if(Array.isArray(a))r.push(...a);else{let c=pt(t[xe]);Wr(c[A],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Qg(e,t){for(let n=ge;n<e.length;n++){let r=e[n],o=r[A].firstChild;o!==null&&Wr(r[A],r,o,t)}e[ft]!==e[Ve]&&t.push(e[ft])}function Vu(e){if(e[Ci]!==null){for(let t of e[Ci])t.impl.addSequence(t);e[Ci].length=0}}var Hu=[];function Yg(e){return e[he]??Kg(e)}function Kg(e){let t=Hu.pop()??Object.create(Xg);return t.lView=e,t}function Jg(e){e.lView[he]!==e&&(e.lView=null,Hu.push(e))}var Xg=H(v({},nr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ro(e.lView)},consumerOnSignalRead(){this.lView[he]=this}});function em(e){let t=e[he]??Object.create(tm);return t.lView=e,t}var tm=H(v({},nr),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=pt(e.lView);for(;t&&!Uu(t[A]);)t=pt(t);t&&Tl(t)},consumerOnSignalRead(){this.lView[he]=this}});function Uu(e){return e.type!==2}function $u(e){if(e[Fr]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Fr])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[M]&8192)}}var nm=100;function Bu(e,t=!0,n=0){let o=e[Qe].rendererFactory,i=!1;i||o.begin?.();try{rm(e,n)}catch(s){throw t&&Og(e,s),s}finally{i||o.end?.()}}function rm(e,t){let n=Ol();try{_c(!0),Ki(e,t);let r=0;for(;no(e);){if(r===nm)throw new I(103,!1);r++,Ki(e,1)}}finally{_c(n)}}function om(e,t,n,r){if(Bt(t))return;let o=t[M],i=!1,s=!1;hs(t);let a=!0,c=null,l=null;i||(Uu(e)?(l=Yg(t),c=ei(l)):Ko()===null?(a=!1,l=em(t),c=ei(l)):t[he]&&(ni(t[he]),t[he]=null));try{_l(t),ch(e.bindingStartIndex),n!==null&&Ru(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&_r(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Tr(t,d,0,null),Di(t,0)}if(s||im(t),$u(t),zu(t,0),e.contentQueries!==null&&yu(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&_r(t,d)}else{let d=e.contentHooks;d!==null&&Tr(t,d,1),Di(t,1)}am(e,t);let f=e.components;f!==null&&Wu(t,f,0);let m=e.viewQuery;if(m!==null&&Zi(2,m,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&_r(t,d)}else{let d=e.viewHooks;d!==null&&Tr(t,d,2),Di(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[bi]){for(let d of t[bi])d();t[bi]=null}i||(Vu(t),t[M]&=-73)}catch(u){throw i||ro(t),u}finally{l!==null&&(La(l,c),a&&Jg(l)),gs()}}function zu(e,t){for(let n=du(e);n!==null;n=fu(n))for(let r=ge;r<n.length;r++){let o=n[r];qu(o,t)}}function im(e){for(let t=du(e);t!==null;t=fu(t)){if(!(t[M]&2))continue;let n=t[Vr];for(let r=0;r<n.length;r++){let o=n[r];Tl(o)}}}function sm(e,t,n){U(18);let r=Ke(t,e);qu(r,n),U(19,r[De])}function qu(e,t){ps(e)&&Ki(e,t)}function Ki(e,t){let r=e[A],o=e[M],i=e[he],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&ti(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)om(r,e,r.template,e[De]);else if(o&8192){$u(e),zu(e,1);let a=r.components;a!==null&&Wu(e,a,1),Vu(e)}}function Wu(e,t,n){for(let r=0;r<t.length;r++)sm(e,t[r],n)}function am(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)ht(~o);else{let i=o,s=n[++r],a=n[++r];uh(s,i);let c=t[i];U(24,c),a(2,c),U(25,c)}}}finally{ht(-1)}}function Gu(e,t){let n=Ol()?64:1088;for(e[Qe].changeDetectionScheduler?.notify(t);e;){e[M]|=n;let r=pt(e);if(Hr(e)&&!r)return e;e=r}return null}function cm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function lm(e,t,n,r=!0){let o=t[A];if(um(o,t,e,n),r){let s=Yi(n,e),a=t[ve],c=a.parentNode(e[ft]);c!==null&&jg(o,e[Me],a,t,c,s)}let i=t[Lr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Ji(e,t){if(e.length<=ge)return;let n=ge+t,r=e[n];if(r){let o=r[wn];o!==null&&o!==e&&Ns(o,r),t>0&&(e[n-1][Ce]=r[Ce]);let i=kr(e,ge+t);Fg(r[A],r);let s=i[eo];s!==null&&s.detachView(i[A]),r[te]=null,r[Ce]=null,r[M]&=-129}return r}function um(e,t,n,r){let o=ge+r,i=n.length;r>0&&(n[o-1][Ce]=t),r<i-ge?(t[Ce]=n[o],dl(n,ge+r,t)):(n.push(t),t[Ce]=null),t[te]=n;let s=t[wn];s!==null&&n!==s&&Zu(s,t);let a=t[eo];a!==null&&a.insertView(e),Li(t),t[M]|=128}function Zu(e,t){let n=e[Vr],r=t[te];if(ut(r))e[M]|=2;else{let o=r[te][xe];t[xe]!==o&&(e[M]|=2)}n===null?e[Vr]=[t]:n.push(t)}var Gr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[A];return Wr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[De]}set context(t){this._lView[De]=t}get destroyed(){return Bt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[te];if(He(t)){let n=t[jr],r=n?n.indexOf(this):-1;r>-1&&(Ji(t,r),kr(n,r))}this._attachedToViewContainer=!1}Lu(this._lView[A],this._lView)}onDestroy(t){xl(this._lView,t)}markForCheck(){Gu(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){Li(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,Bu(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new I(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Hr(this._lView),n=this._lView[wn];n!==null&&!t&&Ns(n,this._lView),Pu(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new I(902,!1);this._appRef=t;let n=Hr(this._lView),r=this._lView[wn];r!==null&&!n&&Zu(r,this._lView),Li(this._lView)}};function Qu(e,t,n,r,o){let i=e.data[t];if(i===null)i=dm(e,t,n,r,o),lh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=sh();i.injectorIndex=s===null?-1:s.injectorIndex}return oo(i,!0),i}function dm(e,t,n,r,o){let i=Nl(),s=Rl(),a=s?i:i&&i.parent,c=e.data[t]=pm(e,a,n,t,r,o);return fm(e,c,i,s),c}function fm(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function pm(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return rh()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var yS=new RegExp(`^(\\d+)*(${Kh}|${Yh})*(.*)`);var hm=()=>null;function zc(e,t){return hm(e,t)}var gm=class{},Yu=class{},Xi=class{resolveComponentFactory(t){throw Error(`No component factory found for ${ce(t)}.`)}},fo=class{static NULL=new Xi},$t=class{};var mm=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>null})}return e})();var _i={},es=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Jr(r);let o=this.injector.get(t,_i,r);return o!==_i||n===_i?o:this.parentInjector.get(t,n,r)}};function qc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=mc(o,a);else if(i==2){let c=a,l=t[++s];r=mc(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function vm(e,t=S.Default){let n=le();if(n===null)return T(e,t);let r=vt();return Jl(r,n,pe(e),t)}function ym(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=wm(s);u===null?a=s:[a,c,l]=u,Cm(e,t,n,a,i,c,l)}i!==null&&r!==null&&Im(n,r,i)}function Im(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new I(-301,!1);r.push(t[o],i)}}function wm(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&mt(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Em(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Em(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function bm(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Cm(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let m=0;m<a;m++){let d=r[m];!c&&mt(d)&&(c=!0,bm(e,n,m)),_h(Zl(n,t),e,d.type)}xm(n,e.data.length,a);for(let m=0;m<a;m++){let d=r[m];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=Tu(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let m=0;m<a;m++){let d=r[m];if(n.mergedAttrs=zl(n.mergedAttrs,d.hostAttrs),Sm(e,n,t,f,d),Tm(f,d,o),s!==null&&s.has(d)){let[x,X]=s.get(d);n.directiveToIndex.set(d.type,[f,x+n.directiveStart,X+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let y=d.type.prototype;!l&&(y.ngOnChanges||y.ngOnInit||y.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(y.ngOnChanges||y.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}Dm(e,n,i)}function Dm(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Wc(0,t,o,r),Wc(1,t,o,r),Zc(t,r,!1);else{let i=n.get(o);Gc(0,t,i,r),Gc(1,t,i,r),Zc(t,r,!0)}}}function Wc(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Ku(t,i)}}function Gc(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Ku(t,s)}}function Ku(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Zc(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||_s(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function Sm(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=jt(o.type,!0)),s=new En(i,mt(o),vm);e.blueprint[r]=s,n[r]=s,Mm(e,t,r,Tu(e,n,o.hostVars,co),o)}function Mm(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;_m(s)!=a&&s.push(a),s.push(n,r,i)}}function _m(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Tm(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;mt(t)&&(n[""]=e)}}function xm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Ju(e,t,n,r,o,i,s,a){let c=t.consts,l=Mc(c,s),u=Qu(t,e,2,r,l);return i&&ym(t,n,u,Mc(c,a),o),u.mergedAttrs=zl(u.mergedAttrs,u.attrs),u.attrs!==null&&qc(u,u.attrs,!1),u.mergedAttrs!==null&&qc(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Xu(e,t){mh(e,t),wl(t)&&e.queries.elementEnd(t)}var Zr=class extends fo{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Ut(t);return new Cn(n,this.ngModule)}};function Nm(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&lo.SignalBased)!==0};return o&&(i.transform=o),i})}function Rm(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function Om(e,t,n){let r=t instanceof me?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new es(n,r):n}function Am(e){let t=e.get($t,null);if(t===null)throw new I(407,!1);let n=e.get(mm,null),r=e.get(bn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function km(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return bu(t,n,n==="svg"?Zp:n==="math"?Qp:null)}var Cn=class extends Yu{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=Nm(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=Rm(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=ug(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){U(22);let i=L(null);try{let s=this.componentDef,a=r?["ng-version","19.2.14"]:dg(this.componentDef.selectors[0]),c=Su(0,null,null,1,0,null,null,null,null,[a],null),l=Om(s,o||this.ngModule,t),u=Am(l),f=u.rendererFactory.createRenderer(null,s),m=r?Eg(f,r,s.encapsulation,l):km(s,f),d=Mu(null,c,null,512|_u(s),null,null,u,f,l,null,vu(m,l,!0));d[Ye]=m,hs(d);let y=null;try{let x=Ju(Ye,c,d,"#host",()=>[this.componentDef],!0,0);m&&(Du(f,m,x),so(m,d)),Ou(c,d,x),Iu(c,x,d),Xu(c,x),n!==void 0&&Pm(x,this.ngContentSelectors,n),y=Ke(x.index,d),d[De]=y[De],Au(c,d,null)}catch(x){throw y!==null&&Wi(y),Wi(d),x}finally{U(23),gs()}return new ts(this.componentType,d)}finally{L(i)}}},ts=class extends gm{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=Ml(n[A],Ye),this.location=bs(this._tNode,n),this.instance=Ke(this._tNode.index,n)[De],this.hostView=this.changeDetectorRef=new Gr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ts(r,o[A],o,t,n);this.previousInputValues.set(t,n);let s=Ke(r.index,o);Gu(s,1)}get injector(){return new dt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function Pm(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Os=(()=>{class e{static __NG_ELEMENT_ID__=Lm}return e})();function Lm(){let e=vt();return jm(e,le())}var Fm=Os,ed=class extends Fm{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return bs(this._hostTNode,this._hostLView)}get injector(){return new dt(this._hostTNode,this._hostLView)}get parentInjector(){let t=vs(this._hostTNode,this._hostLView);if(ql(t)){let n=$r(t,this._hostLView),r=Ur(t),o=n[A].data[r+8];return new dt(o,n)}else return new dt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Qc(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ge}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=zc(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,$c(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Bp(t),a;if(s)a=n;else{let y=n||{};a=y.index,r=y.injector,o=y.projectableNodes,i=y.environmentInjector||y.ngModuleRef}let c=s?t:new Cn(Ut(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let x=(s?l:this.parentInjector).get(me,null);x&&(i=x)}let u=Ut(c.componentType??{}),f=zc(this._lContainer,u?.id??null),m=f?.firstChild??null,d=c.create(l,o,m,i);return this.insertImpl(d.hostView,a,$c(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Kp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[te],l=new ed(c,c[Me],c[te]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return lm(s,o,i,r),t.attachToViewContainerRef(),dl(Ti(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Qc(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Ji(this._lContainer,n);r&&(kr(Ti(this._lContainer),n),Lu(r[A],r))}detach(t){let n=this._adjustIndex(t,-1),r=Ji(this._lContainer,n);return r&&kr(Ti(this._lContainer),n)!=null?new Gr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Qc(e){return e[jr]}function Ti(e){return e[jr]||(e[jr]=[])}function jm(e,t){let n,r=t[e.index];return He(r)?n=r:(n=cm(r,t,null,e),t[e.index]=n,xu(t,n)),Hm(n,t,e,r),new ed(n,e,t)}function Vm(e,t){let n=e[ve],r=n.createComment(""),o=et(t,e),i=n.parentNode(o);return qr(n,i,r,n.nextSibling(o),!1),r}var Hm=Um;function Um(e,t,n,r){if(e[ft])return;let o;n.type&8?o=je(r):o=Vm(t,n),e[ft]=o}var Dn=class{},As=class{};var ns=class extends Dn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Zr(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=hl(t);this._bootstrapComponents=wu(i.bootstrap),this._r3Injector=tu(t,n,[{provide:Dn,useValue:this},{provide:fo,useValue:this.componentFactoryResolver},...r],ce(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},rs=class extends As{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new ns(this.moduleType,t,[])}};var Qr=class extends Dn{injector;componentFactoryResolver=new Zr(this);instance=null;constructor(t){super();let n=new vn([...t.providers,{provide:Dn,useValue:this},{provide:fo,useValue:this.componentFactoryResolver}],t.parent||fs(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function po(e,t,n=null){return new Qr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var $m=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=gl(!1,n.type),o=r.length>0?po([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=E({token:e,providedIn:"environment",factory:()=>new e(T(me))})}return e})();function z(e){return as(()=>{let t=nd(e),n=H(v({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===lu.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get($m).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ne.Emulated,styles:e.styles||Ht,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Ms("NgStandalone"),rd(n);let r=e.dependencies;return n.directiveDefs=Yc(r,!1),n.pipeDefs=Yc(r,!0),n.id=Gm(n),n})}function Bm(e){return Ut(e)||Np(e)}function zm(e){return e!==null}function qm(e,t){if(e==null)return Vt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=lo.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Wm(e){if(e==null)return Vt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function td(e){return as(()=>{let t=nd(e);return rd(t),t})}function nd(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Vt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ht,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:qm(e.inputs,t),outputs:Wm(e.outputs),debugInfo:null}}function rd(e){e.features?.forEach(t=>t(e))}function Yc(e,t){if(!e)return null;let n=t?Rp:Bm;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(zm)}function Gm(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function od(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}var id=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var sd=new w("");var Zm=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new os})}return e})(),os=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function ho(e){return!!e&&typeof e.then=="function"}function Qm(e){return!!e&&typeof e.subscribe=="function"}var Ym=new w("");var ad=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(Ym,{optional:!0})??[];injector=p(Je);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Se(this.injector,o);if(ho(i))n.push(i);else if(Qm(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ks=new w("");function Km(){ri(()=>{throw new I(600,!1)})}function Jm(e){return e.isBoundToModule}var Xm=10;var gt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Uh);afterRenderManager=p(Xh);zonelessEnabled=p(Is);rootEffectScheduler=p(Zm);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Q;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=p(zt).hasPendingTasks.pipe(O(n=>!n));constructor(){p(ao,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(me);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Je.NULL){U(10);let i=n instanceof Yu;if(!this._injector.get(ad).done){let d="";throw new I(405,d)}let a;i?a=n:a=this._injector.get(fo).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=Jm(a)?void 0:this._injector.get(Dn),l=r||a.selector,u=a.create(o,[],l,c),f=u.location.nativeElement,m=u.injector.get(sd,null);return m?.registerApplication(f),u.onDestroy(()=>{this.detachView(u.hostView),xr(this.components,u),m?.unregisterApplication(f)}),this._loadComponent(u),U(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){U(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(mu.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new I(101,!1);let n=L(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,L(n),this.afterTick.next(),U(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get($t,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Xm;)U(14),this.synchronizeOnce(),U(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)ev(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>no(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;xr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(ks,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>xr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new I(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function xr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function ev(e,t,n,r){if(!n&&!no(e))return;Bu(e,t,n&&!r?0:1)}function tv(e,t,n,r){return od(e,Al(),n)?t+al(n)+r:co}function Oe(e,t,n){let r=le(),o=Al();if(od(r,o,t)){let i=_n(),s=ph();Sg(i,s,r,e,t,r[ve],n,!1)}return Oe}function Kc(e,t,n,r,o){Ts(t,e,n,o?"class":"style",r)}function g(e,t,n,r){let o=le(),i=_n(),s=Ye+e,a=o[ve],c=i.firstCreatePass?Ju(s,i,o,t,Ng,nh(),n,r):i.data[s],l=nv(i,o,c,a,t,e);o[s]=l;let u=zp(c);return oo(c,!0),Du(a,l,c),!ku(c)&&Ul()&&Fu(i,o,l,c),(Xp()===0||u)&&so(l,o),eh(),u&&(Ou(i,o,c),Iu(i,c,o)),r!==null&&wg(o,c),g}function h(){let e=vt();Rl()?ah():(e=e.parent,oo(e,!1));let t=e;oh(t)&&ih(),th();let n=_n();return n.firstCreatePass&&Xu(n,t),t.classesWithoutHost!=null&&yh(t)&&Kc(n,t,le(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Ih(t)&&Kc(n,t,le(),t.stylesWithoutHost,!1),h}function V(e,t,n,r){return g(e,t,n,r),h(),V}var nv=(e,t,n,r,o,i)=>($l(!0),bu(r,o,hh()));var Yr="en-US";var rv=Yr;function ov(e){typeof e=="string"&&(rv=e.toLowerCase().replace(/_/g,"-"))}function D(e,t=""){let n=le(),r=_n(),o=e+Ye,i=r.firstCreatePass?Qu(r,o,1,t,null):r.data[o],s=iv(r,n,i,t,e);n[o]=s,Ul()&&Fu(r,n,s,i),oo(i,!1)}var iv=(e,t,n,r,o)=>($l(!0),fg(t[ve],r));function Ps(e){return cd("",e,""),Ps}function cd(e,t,n){let r=le(),o=tv(r,e,t,n);return o!==co&&sv(r,ms(),o),cd}function sv(e,t,n){let r=Yp(t,e);pg(e[ve],r,n)}var is=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ld=(()=>{class e{compileModuleSync(n){return new rs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=hl(n),i=wu(o.declarations).reduce((s,a)=>{let c=Ut(a);return c&&s.push(new Cn(c)),s},[]);return new is(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var av=(()=>{class e{zone=p(Z);changeDetectionScheduler=p(bn);applicationRef=p(gt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),cv=new w("",{factory:()=>!1});function ud({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new Z(H(v({},fd()),{scheduleInRootZone:n})),[{provide:Z,useFactory:e},{provide:mn,multi:!0,useFactory:()=>{let r=p(av,{optional:!0});return()=>r.initialize()}},{provide:mn,multi:!0,useFactory:()=>{let r=p(lv);return()=>{r.initialize()}}},t===!0?{provide:ru,useValue:!0}:[],{provide:ou,useValue:n??nu}]}function dd(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=ud({ngZoneFactory:()=>{let o=fd(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Ms("NgZone_CoalesceEvent"),new Z(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return us([{provide:cv,useValue:!0},{provide:Is,useValue:!1},r])}function fd(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var lv=(()=>{class e{subscription=new $;initialized=!1;zone=p(Z);pendingTasks=p(zt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Z.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Z.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var uv=(()=>{class e{appRef=p(gt);taskService=p(zt);ngZone=p(Z);zonelessEnabled=p(Is);tracing=p(ao,{optional:!0});disableScheduling=p(ru,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new $;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(zr):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(ou,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof qi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?kc:iu;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(zr+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,kc(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function dv(){return typeof $localize<"u"&&$localize.locale||Yr}var pd=new w("",{providedIn:"root",factory:()=>p(pd,S.Optional|S.SkipSelf)||dv()});var ss=new w(""),fv=new w("");function fn(e){return!e.moduleRef}function pv(e){let t=fn(e)?e.r3Injector:e.moduleRef.injector,n=t.get(Z);return n.run(()=>{fn(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Xe,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),fn(e)){let i=()=>t.destroy(),s=e.platformInjector.get(ss);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(ss);s.add(i),e.moduleRef.onDestroy(()=>{xr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return gv(r,n,()=>{let i=t.get(ad);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(pd,Yr);if(ov(s||Yr),!t.get(fv,!0))return fn(e)?t.get(gt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(fn(e)){let c=t.get(gt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return hv(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function hv(e,t){let n=e.injector.get(gt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new I(-403,!1);t.push(e)}function gv(e,t,n){try{let r=n();return ho(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Nr=null;function mv(e=[],t){return Je.create({name:t,providers:[{provide:Xr,useValue:"platform"},{provide:ss,useValue:new Set([()=>Nr=null])},...e]})}function vv(e=[]){if(Nr)return Nr;let t=mv(e);return Nr=t,Km(),yv(t),t}function yv(e){let t=e.get(Ds,null);Se(e,()=>{t?.forEach(n=>n())})}var hd=(()=>{class e{static __NG_ELEMENT_ID__=Iv}return e})();function Iv(e){return wv(vt(),le(),(e&16)===16)}function wv(e,t,n){if(Mn(e)&&!n){let r=Ke(e.index,t);return new Gr(r,r)}else if(e.type&175){let r=t[xe];return new Gr(r,t)}return null}function gd(e){U(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=vv(r),i=[ud({}),{provide:bn,useExisting:uv},...n||[]],s=new Qr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return pv({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{U(9)}}var Jc=class{[bt];constructor(t){this[bt]=t}destroy(){this[bt].destroy()}};var ee=new w("");var yd=null;function Ue(){return yd}function Ls(e){yd??=e}var xn=class{},Fs=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(Id),providedIn:"platform"})}return e})();var Id=(()=>{class e extends Fs{_location;_history;_doc=p(ee);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ue().getBaseHref(this._doc)}onPopState(n){let r=Ue().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Ue().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function wd(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function md(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function tt(e){return e&&e[0]!=="?"?`?${e}`:e}var go=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(bd),providedIn:"root"})}return e})(),Ed=new w(""),bd=(()=>{class e extends go{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(ee).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return wd(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+tt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+tt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+tt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(T(Fs),T(Ed,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wt=(()=>{class e{_subject=new Q;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Cv(md(vd(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+tt(r))}normalize(n){return e.stripTrailingSlash(bv(this._basePath,vd(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+tt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+tt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=tt;static joinWithSlash=wd;static stripTrailingSlash=md;static \u0275fac=function(r){return new(r||e)(T(go))};static \u0275prov=E({token:e,factory:()=>Ev(),providedIn:"root"})}return e})();function Ev(){return new Wt(T(go))}function bv(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function vd(e){return e.replace(/\/index.html$/,"")}function Cv(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}function js(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Vs="browser",Cd="server";function mo(e){return e===Cd}var Nn=class{};var Io=new w(""),Bs=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new I(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(T(Io),T(Z))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Rn=class{_doc;constructor(t){this._doc=t}manager},vo="ng-app-id";function Dd(e){for(let t of e)t.remove()}function Sd(e,t){let n=t.createElement("style");return n.textContent=e,n}function Dv(e,t,n,r){let o=e.head?.querySelectorAll(`style[${vo}="${t}"],link[${vo}="${t}"]`);if(o)for(let i of o)i.removeAttribute(vo),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Us(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var zs=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=mo(i),Dv(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Sd);r?.forEach(o=>this.addUsage(o,this.external,Us))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Dd(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Dd(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Sd(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Us(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(vo,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(T(ee),T(Cs),T(Ss,8),T(Tn))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Hs={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},qs=/%COMP%/g;var _d="%COMP%",Sv=`_nghost-${_d}`,Mv=`_ngcontent-${_d}`,_v=!0,Tv=new w("",{providedIn:"root",factory:()=>_v});function xv(e){return Mv.replace(qs,e)}function Nv(e){return Sv.replace(qs,e)}function Td(e,t){return t.map(n=>n.replace(qs,e))}var Ws=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=mo(a),this.defaultRenderer=new On(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ne.ShadowDom&&(r=H(v({},r),{encapsulation:Ne.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof yo?o.applyToHost(n):o instanceof An&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,f=this.platformIsServer,m=this.tracingService;switch(r.encapsulation){case Ne.Emulated:i=new yo(c,l,r,this.appId,u,s,a,f,m);break;case Ne.ShadowDom:return new $s(c,l,n,r,s,a,this.nonce,f,m);default:i=new An(c,l,r,u,s,a,f,m);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(T(Bs),T(zs),T(Cs),T(Tv),T(ee),T(Tn),T(Z),T(Ss),T(ao,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),On=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Hs[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Md(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Md(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new I(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Hs[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Hs[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(qt.DashCase|qt.Important)?t.style.setProperty(n,r,o&qt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&qt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Ue().getGlobalEventTarget(this.doc,t),!t))throw new I(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Md(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var $s=class extends On{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=Td(o.id,u);for(let m of u){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=m,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let m of f){let d=Us(m,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},An=class extends On{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?Td(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},yo=class extends An{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=xv(u),this.hostAttr=Nv(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var wo=class e extends xn{supportsDOMEvents=!0;static makeCurrent(){Ls(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Av();return n==null?null:kv(n)}resetBaseElement(){kn=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return js(document.cookie,t)}},kn=null;function Av(){return kn=kn||document.head.querySelector("base"),kn?kn.getAttribute("href"):null}function kv(e){return new URL(e,document.baseURI).pathname}var Pv=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Nd=(()=>{class e extends Rn{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(T(ee))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),xd=["alt","control","meta","shift"],Lv={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Fv={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Rd=(()=>{class e extends Rn{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ue().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),xd.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=Lv[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),xd.forEach(s=>{if(s!==o){let a=Fv[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(T(ee))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function Gs(e,t){return gd(v({rootComponent:e},jv(t)))}function jv(e){return{appProviders:[...Bv,...e?.providers??[]],platformProviders:$v}}function Vv(){wo.makeCurrent()}function Hv(){return new Xe}function Uv(){return hu(document),document}var $v=[{provide:Tn,useValue:Vs},{provide:Ds,useValue:Vv,multi:!0},{provide:ee,useFactory:Uv}];var Bv=[{provide:Xr,useValue:"root"},{provide:Xe,useFactory:Hv},{provide:Io,useClass:Nd,multi:!0,deps:[ee]},{provide:Io,useClass:Rd,multi:!0,deps:[ee]},Ws,zs,Bs,{provide:$t,useExisting:Ws},{provide:Nn,useClass:Pv},[]];var Od=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(T(ee))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _="primary",Wn=Symbol("RouteTitle"),Js=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Jt(e){return new Js(e)}function qv(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function Wv(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Ae(e[n],t[n]))return!1;return!0}function Ae(e,t){let n=e?Xs(e):void 0,r=t?Xs(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Hd(e[o],t[o]))return!1;return!0}function Xs(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Hd(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function Ud(e){return e.length>0?e[e.length-1]:null}function ot(e){return mi(e)?e:ho(e)?B(Promise.resolve(e)):C(e)}var Gv={exact:Bd,subset:zd},$d={exact:Zv,subset:Qv,ignored:()=>!0};function Ad(e,t,n){return Gv[n.paths](e.root,t.root,n.matrixParams)&&$d[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function Zv(e,t){return Ae(e,t)}function Bd(e,t,n){if(!It(e.segments,t.segments)||!Co(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Bd(e.children[r],t.children[r],n))return!1;return!0}function Qv(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Hd(e[n],t[n]))}function zd(e,t,n){return qd(e,t,t.segments,n)}function qd(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!It(o,n)||t.hasChildren()||!Co(o,n,r))}else if(e.segments.length===n.length){if(!It(e.segments,n)||!Co(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!zd(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!It(e.segments,o)||!Co(e.segments,o,r)||!e.children[_]?!1:qd(e.children[_],t,i,r)}}function Co(e,t,n){return t.every((r,o)=>$d[n](e[o].parameters,r.parameters))}var Be=class{root;queryParams;fragment;_queryParamMap;constructor(t=new P([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Jt(this.queryParams),this._queryParamMap}toString(){return Jv.serialize(this)}},P=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Do(this)}},yt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Jt(this.parameters),this._parameterMap}toString(){return Gd(this)}};function Yv(e,t){return It(e,t)&&e.every((n,r)=>Ae(n.parameters,t[r].parameters))}function It(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function Kv(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===_&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==_&&(n=n.concat(t(o,r)))}),n}var Fo=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new Xt,providedIn:"root"})}return e})(),Xt=class{parse(t){let n=new ta(t);return new Be(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Pn(t.root,!0)}`,r=ty(t.queryParams),o=typeof t.fragment=="string"?`#${Xv(t.fragment)}`:"";return`${n}${r}${o}`}},Jv=new Xt;function Do(e){return e.segments.map(t=>Gd(t)).join("/")}function Pn(e,t){if(!e.hasChildren())return Do(e);if(t){let n=e.children[_]?Pn(e.children[_],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==_&&r.push(`${o}:${Pn(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=Kv(e,(r,o)=>o===_?[Pn(e.children[_],!1)]:[`${o}:${Pn(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[_]!=null?`${Do(e)}/${n[0]}`:`${Do(e)}/(${n.join("//")})`}}function Wd(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Eo(e){return Wd(e).replace(/%3B/gi,";")}function Xv(e){return encodeURI(e)}function ea(e){return Wd(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function So(e){return decodeURIComponent(e)}function kd(e){return So(e.replace(/\+/g,"%20"))}function Gd(e){return`${ea(e.path)}${ey(e.parameters)}`}function ey(e){return Object.entries(e).map(([t,n])=>`;${ea(t)}=${ea(n)}`).join("")}function ty(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Eo(n)}=${Eo(o)}`).join("&"):`${Eo(n)}=${Eo(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var ny=/^[^\/()?;#]+/;function Zs(e){let t=e.match(ny);return t?t[0]:""}var ry=/^[^\/()?;=#]+/;function oy(e){let t=e.match(ry);return t?t[0]:""}var iy=/^[^=?&#]+/;function sy(e){let t=e.match(iy);return t?t[0]:""}var ay=/^[^&#]+/;function cy(e){let t=e.match(ay);return t?t[0]:""}var ta=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new P([],{}):new P([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[_]=new P(t,n)),r}parseSegment(){let t=Zs(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new I(4009,!1);return this.capture(t),new yt(So(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=oy(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Zs(this.remaining);o&&(r=o,this.capture(r))}t[So(n)]=So(r)}parseQueryParam(t){let n=sy(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=cy(this.remaining);s&&(r=s,this.capture(r))}let o=kd(n),i=kd(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Zs(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new I(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=_);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[_]:new P([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new I(4011,!1)}};function Zd(e){return e.segments.length>0?new P([],{[_]:e}):e}function Qd(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Qd(o);if(r===_&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new P(e.segments,t);return ly(n)}function ly(e){if(e.numberOfChildren===1&&e.children[_]){let t=e.children[_];return new P(e.segments.concat(t.segments),t.children)}return e}function en(e){return e instanceof Be}function uy(e,t,n=null,r=null){let o=Yd(e);return Kd(o,t,n,r)}function Yd(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new P(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Zd(r);return t??o}function Kd(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Qs(o,o,o,n,r);let i=dy(t);if(i.toRoot())return Qs(o,o,new P([],{}),n,r);let s=fy(i,o,e),a=s.processChildren?Fn(s.segmentGroup,s.index,i.commands):Xd(s.segmentGroup,s.index,i.commands);return Qs(o,s.segmentGroup,a,n,r)}function _o(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Vn(e){return typeof e=="object"&&e!=null&&e.outlets}function Qs(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=Jd(e,t,n);let a=Zd(Qd(s));return new Be(a,i,o)}function Jd(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Jd(i,t,n)}),new P(e.segments,r)}var To=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&_o(r[0]))throw new I(4003,!1);let o=r.find(Vn);if(o&&o!==Ud(r))throw new I(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function dy(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new To(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new To(n,t,r)}var Qt=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function fy(e,t,n){if(e.isAbsolute)return new Qt(t,!0,0);if(!n)return new Qt(t,!1,NaN);if(n.parent===null)return new Qt(n,!0,0);let r=_o(e.commands[0])?0:1,o=n.segments.length-1+r;return py(n,o,e.numberOfDoubleDots)}function py(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new I(4005,!1);o=r.segments.length}return new Qt(r,!1,o-i)}function hy(e){return Vn(e[0])?e[0].outlets:{[_]:e}}function Xd(e,t,n){if(e??=new P([],{}),e.segments.length===0&&e.hasChildren())return Fn(e,t,n);let r=gy(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new P(e.segments.slice(0,r.pathIndex),{});return i.children[_]=new P(e.segments.slice(r.pathIndex),e.children),Fn(i,0,o)}else return r.match&&o.length===0?new P(e.segments,{}):r.match&&!e.hasChildren()?na(e,t,n):r.match?Fn(e,0,o):na(e,t,n)}function Fn(e,t,n){if(n.length===0)return new P(e.segments,{});{let r=hy(n),o={};if(Object.keys(r).some(i=>i!==_)&&e.children[_]&&e.numberOfChildren===1&&e.children[_].segments.length===0){let i=Fn(e.children[_],t,n);return new P(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Xd(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new P(e.segments,o)}}function gy(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Vn(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!Ld(c,l,s))return i;r+=2}else{if(!Ld(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function na(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Vn(i)){let c=my(i.outlets);return new P(r,c)}if(o===0&&_o(n[0])){let c=e.segments[t];r.push(new yt(c.path,Pd(n[0]))),o++;continue}let s=Vn(i)?i.outlets[_]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&_o(a)?(r.push(new yt(s,Pd(a))),o+=2):(r.push(new yt(s,{})),o++)}return new P(r,{})}function my(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=na(new P([],{}),0,r))}),t}function Pd(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Ld(e,t,n){return e==n.path&&Ae(t,n.parameters)}var Mo="imperative",J=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(J||{}),ye=class{id;url;constructor(t,n){this.id=t,this.url=n}},tn=class extends ye{type=J.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},nt=class extends ye{urlAfterRedirects;type=J.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},de=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(de||{}),xo=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(xo||{}),$e=class extends ye{reason;code;type=J.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},rt=class extends ye{reason;code;type=J.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Hn=class extends ye{error;target;type=J.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},No=class extends ye{urlAfterRedirects;state;type=J.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ra=class extends ye{urlAfterRedirects;state;type=J.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},oa=class extends ye{urlAfterRedirects;state;shouldActivate;type=J.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ia=class extends ye{urlAfterRedirects;state;type=J.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},sa=class extends ye{urlAfterRedirects;state;type=J.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},aa=class{route;type=J.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ca=class{route;type=J.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},la=class{snapshot;type=J.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ua=class{snapshot;type=J.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},da=class{snapshot;type=J.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},fa=class{snapshot;type=J.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Un=class{},nn=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function vy(e,t){return e.providers&&!e._injector&&(e._injector=po(e.providers,t,`Route: ${e.path}`)),e._injector??t}function _e(e){return e.outlet||_}function yy(e,t){let n=e.filter(r=>_e(r)===t);return n.push(...e.filter(r=>_e(r)!==t)),n}function Gn(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var pa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Gn(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Zn(this.rootInjector)}},Zn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new pa(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(T(me))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ro=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=ha(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=ha(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=ga(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return ga(t,this._root).map(n=>n.value)}};function ha(e,t){if(e===t.value)return t;for(let n of t.children){let r=ha(e,n);if(r)return r}return null}function ga(e,t){if(e===t.value)return[t];for(let n of t.children){let r=ga(e,n);if(r.length)return r.unshift(t),r}return[]}var ue=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Zt(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Oo=class extends Ro{snapshot;constructor(t,n){super(t),this.snapshot=n,Da(this,t)}toString(){return this.snapshot.toString()}};function ef(e){let t=Iy(e),n=new Y([new yt("",{})]),r=new Y({}),o=new Y({}),i=new Y({}),s=new Y(""),a=new wt(n,r,i,s,o,_,e,t.root);return a.snapshot=t.root,new Oo(new ue(a,[]),t)}function Iy(e){let t={},n={},r={},o="",i=new Yt([],t,r,o,n,_,e,null,{});return new ko("",new ue(i,[]))}var wt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(O(l=>l[Wn]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(O(t=>Jt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(O(t=>Jt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Ao(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:v(v({},t.params),e.params),data:v(v({},t.data),e.data),resolve:v(v(v(v({},e.data),t.data),o?.data),e._resolvedData)}:r={params:v({},e.params),data:v({},e.data),resolve:v(v({},e.data),e._resolvedData??{})},o&&nf(o)&&(r.resolve[Wn]=o.title),r}var Yt=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Wn]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Jt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Jt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},ko=class extends Ro{url;constructor(t,n){super(n),this.url=t,Da(this,n)}toString(){return tf(this._root)}};function Da(e,t){t.value._routerState=e,t.children.forEach(n=>Da(e,n))}function tf(e){let t=e.children.length>0?` { ${e.children.map(tf).join(", ")} } `:"";return`${e.value}${t}`}function Ys(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Ae(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Ae(t.params,n.params)||e.paramsSubject.next(n.params),Wv(t.url,n.url)||e.urlSubject.next(n.url),Ae(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function ma(e,t){let n=Ae(e.params,t.params)&&Yv(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||ma(e.parent,t.parent))}function nf(e){return typeof e.title=="string"||e.title===null}var wy=new w(""),rf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=_;activateEvents=new ie;deactivateEvents=new ie;attachEvents=new ie;detachEvents=new ie;routerOutletData=au(void 0);parentContexts=p(Zn);location=p(Os);changeDetector=p(hd);inputBinder=p(jo,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new I(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new I(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new I(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new I(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new va(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=td({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[bl]})}return e})(),va=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===wt?this.route:t===Zn?this.childContexts:t===wy?this.outletData:this.parent.get(t,n)}},jo=new w("");var of=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=z({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&V(0,"router-outlet")},dependencies:[rf],encapsulation:2})}return e})();function Sa(e){let t=e.children&&e.children.map(Sa),n=t?H(v({},e),{children:t}):v({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==_&&(n.component=of),n}function Ey(e,t,n){let r=$n(e,t._root,n?n._root:void 0);return new Oo(r,t)}function $n(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=by(e,t,n);return new ue(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>$n(e,a)),s}}let r=Cy(t.value),o=t.children.map(i=>$n(e,i));return new ue(r,o)}}function by(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return $n(e,r,o);return $n(e,r)})}function Cy(e){return new wt(new Y(e.url),new Y(e.params),new Y(e.queryParams),new Y(e.fragment),new Y(e.data),e.outlet,e.component,e)}var Bn=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},sf="ngNavigationCancelingError";function Po(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=en(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=af(!1,de.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function af(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[sf]=!0,n.cancellationCode=t,n}function Dy(e){return cf(e)&&en(e.url)}function cf(e){return!!e&&e[sf]}var Sy=(e,t,n,r)=>O(o=>(new ya(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),ya=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Ys(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Zt(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Zt(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Zt(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Zt(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new fa(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ua(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Ys(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Ys(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Lo=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Kt=class{component;route;constructor(t,n){this.component=t,this.route=n}};function My(e,t,n){let r=e._root,o=t?t._root:null;return Ln(r,o,n,[r.value])}function _y(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function on(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!rl(e)?e:t.get(e):r}function Ln(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Zt(t);return e.children.forEach(s=>{Ty(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>jn(a,n.getContext(s),o)),o}function Ty(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=xy(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Lo(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Ln(e,t,a?a.children:null,r,o):Ln(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Kt(a.outlet.component,s))}else s&&jn(t,a,o),o.canActivateChecks.push(new Lo(r)),i.component?Ln(e,null,a?a.children:null,r,o):Ln(e,null,n,r,o);return o}function xy(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!It(e.url,t.url);case"pathParamsOrQueryParamsChange":return!It(e.url,t.url)||!Ae(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ma(e,t)||!Ae(e.queryParams,t.queryParams);case"paramsChange":default:return!ma(e,t)}}function jn(e,t,n){let r=Zt(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?jn(s,t.children.getContext(i),n):jn(s,null,n):jn(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Kt(t.outlet.component,o)):n.canDeactivateChecks.push(new Kt(null,o)):n.canDeactivateChecks.push(new Kt(null,o))}function Qn(e){return typeof e=="function"}function Ny(e){return typeof e=="boolean"}function Ry(e){return e&&Qn(e.canLoad)}function Oy(e){return e&&Qn(e.canActivate)}function Ay(e){return e&&Qn(e.canActivateChild)}function ky(e){return e&&Qn(e.canDeactivate)}function Py(e){return e&&Qn(e.canMatch)}function lf(e){return e instanceof Pe||e?.name==="EmptyError"}var bo=Symbol("INITIAL_VALUE");function rn(){return Ee(e=>Cr(e.map(t=>t.pipe(Le(1),Ii(bo)))).pipe(O(t=>{for(let n of t)if(n!==!0){if(n===bo)return bo;if(n===!1||Ly(n))return n}return!0}),we(t=>t!==bo),Le(1)))}function Ly(e){return en(e)||e instanceof Bn}function Fy(e,t){return W(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(H(v({},n),{guardsResult:!0})):jy(s,r,o,e).pipe(W(a=>a&&Ny(a)?Vy(r,i,e,t):C(a)),O(a=>H(v({},n),{guardsResult:a})))})}function jy(e,t,n,r){return B(e).pipe(W(o=>zy(o.component,o.route,n,t,r)),Fe(o=>o!==!0,!0))}function Vy(e,t,n,r){return B(t).pipe(Ot(o=>Rt(Uy(o.route.parent,r),Hy(o.route,r),By(e,o.path,n),$y(e,o.route,n))),Fe(o=>o!==!0,!0))}function Hy(e,t){return e!==null&&t&&t(new da(e)),C(!0)}function Uy(e,t){return e!==null&&t&&t(new la(e)),C(!0)}function $y(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>Dr(()=>{let s=Gn(t)??n,a=on(i,s),c=Oy(a)?a.canActivate(t,e):Se(s,()=>a(t,e));return ot(c).pipe(Fe())}));return C(o).pipe(rn())}function By(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>_y(s)).filter(s=>s!==null).map(s=>Dr(()=>{let a=s.guards.map(c=>{let l=Gn(s.node)??n,u=on(c,l),f=Ay(u)?u.canActivateChild(r,e):Se(l,()=>u(r,e));return ot(f).pipe(Fe())});return C(a).pipe(rn())}));return C(i).pipe(rn())}function zy(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=Gn(t)??o,l=on(a,c),u=ky(l)?l.canDeactivate(e,t,n,r):Se(c,()=>l(e,t,n,r));return ot(u).pipe(Fe())});return C(s).pipe(rn())}function qy(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=on(s,e),c=Ry(a)?a.canLoad(t,n):Se(e,()=>a(t,n));return ot(c)});return C(i).pipe(rn(),uf(r))}function uf(e){return fi(K(t=>{if(typeof t!="boolean")throw Po(e,t)}),O(t=>t===!0))}function Wy(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=on(s,e),c=Py(a)?a.canMatch(t,n):Se(e,()=>a(t,n));return ot(c)});return C(i).pipe(rn(),uf(r))}var zn=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},qn=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Gt(e){return Nt(new zn(e))}function Gy(e){return Nt(new I(4e3,!1))}function Zy(e){return Nt(af(!1,de.GuardRejected))}var Ia=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[_])return Gy(`${t.redirectTo}`);o=o.children[_]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:l,routeConfig:u,url:f,outlet:m,params:d,data:y,title:x}=o,X=Se(i,()=>a({params:d,data:y,queryParams:c,fragment:l,routeConfig:u,url:f,outlet:m,title:x}));if(X instanceof Be)throw new qn(X);n=X}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new qn(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Be(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new P(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new I(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},wa={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Qy(e,t,n,r,o){let i=df(e,t,n);return i.matched?(r=vy(t,r),Wy(r,t,n,o).pipe(O(s=>s===!0?i:v({},wa)))):C(i)}function df(e,t,n){if(t.path==="**")return Yy(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?v({},wa):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||qv)(n,e,t);if(!o)return v({},wa);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?v(v({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function Yy(e){return{matched:!0,parameters:e.length>0?Ud(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Fd(e,t,n,r){return n.length>0&&Xy(e,n,r)?{segmentGroup:new P(t,Jy(r,new P(n,e.children))),slicedSegments:[]}:n.length===0&&eI(e,n,r)?{segmentGroup:new P(e.segments,Ky(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new P(e.segments,e.children),slicedSegments:n}}function Ky(e,t,n,r){let o={};for(let i of n)if(Vo(e,t,i)&&!r[_e(i)]){let s=new P([],{});o[_e(i)]=s}return v(v({},r),o)}function Jy(e,t){let n={};n[_]=t;for(let r of e)if(r.path===""&&_e(r)!==_){let o=new P([],{});n[_e(r)]=o}return n}function Xy(e,t,n){return n.some(r=>Vo(e,t,r)&&_e(r)!==_)}function eI(e,t,n){return n.some(r=>Vo(e,t,r))}function Vo(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function tI(e,t,n){return t.length===0&&!e.children[n]}var Ea=class{};function nI(e,t,n,r,o,i,s="emptyOnly"){return new ba(e,t,n,r,o,s,i).recognize()}var rI=31,ba=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Ia(this.urlSerializer,this.urlTree)}noMatchError(t){return new I(4002,`'${t.segmentGroup}'`)}recognize(){let t=Fd(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(O(({children:n,rootSnapshot:r})=>{let o=new ue(r,n),i=new ko("",o),s=uy(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Yt([],Object.freeze({}),Object.freeze(v({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),_,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,_,n).pipe(O(r=>({children:r,rootSnapshot:n})),We(r=>{if(r instanceof qn)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof zn?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(O(s=>s instanceof ue?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return B(i).pipe(Ot(s=>{let a=r.children[s],c=yy(n,s);return this.processSegmentGroup(t,c,a,s,o)}),yi((s,a)=>(s.push(...a),s)),Ge(null),vi(),W(s=>{if(s===null)return Gt(r);let a=ff(s);return oI(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return B(n).pipe(Ot(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(We(l=>{if(l instanceof zn)return C(null);throw l}))),Fe(c=>!!c),We(c=>{if(lf(c))return tI(r,o,i)?C(new Ea):Gt(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return _e(r)!==s&&(s===_||!Vo(o,i,r))?Gt(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Gt(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:f,remainingSegments:m}=df(n,o,i);if(!c)return Gt(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>rI&&(this.allowRedirects=!1));let d=new Yt(i,l,Object.freeze(v({},this.urlTree.queryParams)),this.urlTree.fragment,jd(o),_e(o),o.component??o._loadedComponent??null,o,Vd(o)),y=Ao(d,a,this.paramsInheritanceStrategy);d.params=Object.freeze(y.params),d.data=Object.freeze(y.data);let x=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,f,d,t);return this.applyRedirects.lineralizeSegments(o,x).pipe(W(X=>this.processSegment(t,r,n,X.concat(m),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=Qy(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ee(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Ee(({routes:l})=>{let u=r._loadedInjector??t,{parameters:f,consumedSegments:m,remainingSegments:d}=c,y=new Yt(m,f,Object.freeze(v({},this.urlTree.queryParams)),this.urlTree.fragment,jd(r),_e(r),r.component??r._loadedComponent??null,r,Vd(r)),x=Ao(y,s,this.paramsInheritanceStrategy);y.params=Object.freeze(x.params),y.data=Object.freeze(x.data);let{segmentGroup:X,slicedSegments:ne}=Fd(n,m,d,l);if(ne.length===0&&X.hasChildren())return this.processChildren(u,l,X,y).pipe(O(er=>new ue(y,er)));if(l.length===0&&ne.length===0)return C(new ue(y,[]));let Df=_e(r)===i;return this.processSegment(u,l,X,ne,Df?_:i,!0,y).pipe(O(er=>new ue(y,er instanceof ue?[er]:[])))}))):Gt(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):qy(t,n,r,this.urlSerializer).pipe(W(o=>o?this.configLoader.loadChildren(t,n).pipe(K(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):Zy(n))):C({routes:[],injector:t})}};function oI(e){e.sort((t,n)=>t.value.outlet===_?-1:n.value.outlet===_?1:t.value.outlet.localeCompare(n.value.outlet))}function iI(e){let t=e.value.routeConfig;return t&&t.path===""}function ff(e){let t=[],n=new Set;for(let r of e){if(!iI(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=ff(r.children);t.push(new ue(r.value,o))}return t.filter(r=>!n.has(r))}function jd(e){return e.data||{}}function Vd(e){return e.resolve||{}}function sI(e,t,n,r,o,i){return W(s=>nI(e,t,n,r,s.extractedUrl,o,i).pipe(O(({state:a,tree:c})=>H(v({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function aI(e,t){return W(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of pf(c))s.add(l);let a=0;return B(s).pipe(Ot(c=>i.has(c)?cI(c,r,e,t):(c.data=Ao(c,c.parent,e).resolve,C(void 0))),K(()=>a++),At(1),W(c=>a===s.size?C(n):re))})}function pf(e){let t=e.children.map(n=>pf(n)).flat();return[e,...t]}function cI(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!nf(o)&&(i[Wn]=o.title),lI(i,e,t,r).pipe(O(s=>(e._resolvedData=s,e.data=Ao(e,e.parent,n).resolve,null)))}function lI(e,t,n,r){let o=Xs(e);if(o.length===0)return C({});let i={};return B(o).pipe(W(s=>uI(e[s],t,n,r).pipe(Fe(),K(a=>{if(a instanceof Bn)throw Po(new Xt,a);i[s]=a}))),At(1),O(()=>i),We(s=>lf(s)?re:Nt(s)))}function uI(e,t,n,r){let o=Gn(t)??r,i=on(e,o),s=i.resolve?i.resolve(t,n):Se(o,()=>i(t,n));return ot(s)}function Ks(e){return Ee(t=>{let n=e(t);return n?B(n).pipe(O(()=>t)):C(t)})}var hf=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===_);return r}getResolvedTitleForRoute(n){return n.data[Wn]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(dI),providedIn:"root"})}return e})(),dI=(()=>{class e extends hf{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(T(Od))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ho=new w("",{providedIn:"root",factory:()=>({})}),Uo=new w(""),gf=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(ld);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=ot(n.loadComponent()).pipe(O(mf),K(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),dn(()=>{this.componentLoaders.delete(n)})),o=new xt(r,()=>new Q).pipe(Tt());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=fI(r,this.compiler,n,this.onLoadEndListener).pipe(dn(()=>{this.childrenLoaders.delete(r)})),s=new xt(i,()=>new Q).pipe(Tt());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fI(e,t,n,r){return ot(e.loadChildren()).pipe(O(mf),W(o=>o instanceof As||Array.isArray(o)?C(o):B(t.compileModuleAsync(o))),O(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Uo,[],{optional:!0,self:!0}).flat()),{routes:s.map(Sa),injector:i}}))}function pI(e){return e&&typeof e=="object"&&"default"in e}function mf(e){return pI(e)?e.default:e}var Ma=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(hI),providedIn:"root"})}return e})(),hI=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vf=new w("");var yf=new w(""),If=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new Q;transitionAbortSubject=new Q;configLoader=p(gf);environmentInjector=p(me);destroyRef=p(io);urlSerializer=p(Fo);rootContexts=p(Zn);location=p(Wt);inputBindingEnabled=p(jo,{optional:!0})!==null;titleStrategy=p(hf);options=p(Ho,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Ma);createViewTransition=p(vf,{optional:!0});navigationErrorHandler=p(yf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new aa(o)),r=o=>this.events.next(new ca(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(H(v({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new Y(null),this.transitions.pipe(we(r=>r!==null),Ee(r=>{let o=!1,i=!1;return C(r).pipe(Ee(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),re;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?H(v({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let l="";return this.events.next(new rt(s.id,this.urlSerializer.serialize(s.rawUrl),l,xo.IgnoredSameUrlNavigation)),s.resolve(!1),re}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(Ee(l=>(this.events.next(new tn(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?re:Promise.resolve(l))),sI(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),K(l=>{r.targetSnapshot=l.targetSnapshot,r.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=H(v({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let u=new No(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:l,extractedUrl:u,source:f,restoredState:m,extras:d}=s,y=new tn(l,this.urlSerializer.serialize(u),f,m);this.events.next(y);let x=ef(this.rootComponentType).snapshot;return this.currentTransition=r=H(v({},s),{targetSnapshot:x,urlAfterRedirects:u,extras:H(v({},d),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,C(r)}else{let l="";return this.events.next(new rt(s.id,this.urlSerializer.serialize(s.extractedUrl),l,xo.IgnoredByUrlHandlingStrategy)),s.resolve(!1),re}}),K(s=>{let a=new ra(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),O(s=>(this.currentTransition=r=H(v({},s),{guards:My(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),Fy(this.environmentInjector,s=>this.events.next(s)),K(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw Po(this.urlSerializer,s.guardsResult);let a=new oa(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),we(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",de.GuardRejected),!1)),Ks(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(K(a=>{let c=new ia(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),Ee(a=>{let c=!1;return C(a).pipe(aI(this.paramsInheritanceStrategy,this.environmentInjector),K({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",de.NoDataFromResolver)}}))}),K(a=>{let c=new sa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Ks(s=>{let a=c=>{let l=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&l.push(this.configLoader.loadComponent(c.routeConfig).pipe(K(u=>{c.component=u}),O(()=>{})));for(let u of c.children)l.push(...a(u));return l};return Cr(a(s.targetSnapshot.root)).pipe(Ge(null),Le(1))}),Ks(()=>this.afterPreactivation()),Ee(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?B(c).pipe(O(()=>r)):C(r)}),O(s=>{let a=Ey(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=H(v({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),K(()=>{this.events.next(new Un)}),Sy(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Le(1),K({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new nt(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),wi(this.transitionAbortSubject.pipe(K(s=>{throw s}))),dn(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),We(s=>{if(this.destroyed)return r.resolve(!1),re;if(i=!0,cf(s))this.events.next(new $e(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),Dy(s)?this.events.next(new nn(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Hn(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Se(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Bn){let{message:l,cancellationCode:u}=Po(this.urlSerializer,c);this.events.next(new $e(r.id,this.urlSerializer.serialize(r.extractedUrl),l,u)),this.events.next(new nn(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return re}))}))}cancelNavigationTransition(n,r,o){let i=new $e(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function gI(e){return e!==Mo}var mI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(vI),providedIn:"root"})}return e})(),Ca=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},vI=(()=>{class e extends Ca{static \u0275fac=(()=>{let n;return function(o){return(n||(n=ys(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),wf=(()=>{class e{urlSerializer=p(Fo);options=p(Ho,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Wt);urlHandlingStrategy=p(Ma);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Be;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Be?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=ef(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(yI),providedIn:"root"})}return e})(),yI=(()=>{class e extends wf{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof tn?this.updateStateMemento():n instanceof rt?this.commitTransition(r):n instanceof No?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Un?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof $e&&(n.code===de.GuardRejected||n.code===de.NoDataFromResolver)?this.restoreHistory(r):n instanceof Hn?this.restoreHistory(r,!0):n instanceof nt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=v(v({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=v(v({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=ys(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ef(e,t){e.events.pipe(we(n=>n instanceof nt||n instanceof $e||n instanceof Hn||n instanceof rt),O(n=>n instanceof nt||n instanceof rt?0:(n instanceof $e?n.code===de.Redirect||n.code===de.SupersededByNewNavigation:!1)?2:1),we(n=>n!==2),Le(1)).subscribe(()=>{t()})}var II={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},wI={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},_a=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(id);stateManager=p(wf);options=p(Ho,{optional:!0})||{};pendingTasks=p(zt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(If);urlSerializer=p(Fo);location=p(Wt);urlHandlingStrategy=p(Ma);_events=new Q;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(mI);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Uo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(jo,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new $;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof $e&&r.code!==de.Redirect&&r.code!==de.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof nt)this.navigated=!0;else if(r instanceof nn){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=v({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||gI(o.source)},s);this.scheduleNavigation(a,Mo,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}bI(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Mo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=v({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Sa),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=v(v({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let f;try{let m=o?o.snapshot:this.routerState.snapshot.root;f=Yd(m)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return Kd(f,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=en(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Mo,null,r)}navigate(n,r={skipLocationChange:!1}){return EI(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=v({},II):r===!1?o=v({},wI):o=r,en(n))return Ad(this.currentUrlTree,n,o);let i=this.parseUrl(n);return Ad(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((f,m)=>{a=f,c=m});let u=this.pendingTasks.add();return Ef(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function EI(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new I(4008,!1)}function bI(e){return!(e instanceof Un)&&!(e instanceof nn)}var CI=new w("");function Ta(e,...t){return us([{provide:Uo,multi:!0,useValue:e},[],{provide:wt,useFactory:DI,deps:[_a]},{provide:ks,multi:!0,useFactory:SI},t.map(n=>n.\u0275providers)])}function DI(e){return e.routerState.root}function SI(){let e=p(Je);return t=>{let n=e.get(gt);if(t!==n.components[0])return;let r=e.get(_a),o=e.get(MI);e.get(_I)===1&&r.initialNavigation(),e.get(TI,null,S.Optional)?.setUpPreloading(),e.get(CI,null,S.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var MI=new w("",{factory:()=>new Q}),_I=new w("",{providedIn:"root",factory:()=>1});var TI=new w("");var Yn=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-home"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"home works!"),h())},encapsulation:2})};var sn=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-projects"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"projects works!"),h())},encapsulation:2})};var bf=[{path:"",component:Yn},{path:"home",component:Yn},{path:"projects",component:sn},{path:"**",redirectTo:"",pathMatch:"full"}];var Cf={providers:[dd({eventCoalescing:!0}),Ta(bf)]};var Et=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(Et||{});function Kn(e,t){return{type:Et.Trigger,name:e,definitions:t,options:{}}}function Jn(e,t=null){return{type:Et.Animate,styles:t,timings:e}}function ze(e){return{type:Et.Style,styles:e,offset:null}}function xa(e){return{type:Et.Keyframes,steps:e}}function Xn(e,t,n=null){return{type:Et.Transition,expr:e,animation:t,options:n}}var $o=class e{currentTitle="";titles=["Full Stack Developer Trainee","Angular Developer","Java Enthusiast","Problem Solver","Tech Innovator"];currentIndex=0;typingInterval;titleInterval;ngOnInit(){this.startTypingAnimation()}ngOnDestroy(){this.typingInterval&&clearInterval(this.typingInterval),this.titleInterval&&clearInterval(this.titleInterval)}startTypingAnimation(){this.typeTitle(),this.titleInterval=setInterval(()=>{this.currentIndex=(this.currentIndex+1)%this.titles.length,this.currentTitle="",this.typeTitle()},4e3)}typeTitle(){let t=this.titles[this.currentIndex],n=0;this.typingInterval=setInterval(()=>{n<t.length?(this.currentTitle+=t.charAt(n),n++):clearInterval(this.typingInterval)},100)}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-hero"]],decls:77,vars:9,consts:[[1,"hero-container"],[1,"hero-background"],[1,"floating-shapes"],[1,"shape","shape-1"],[1,"shape","shape-2"],[1,"shape","shape-3"],[1,"shape","shape-4"],[1,"shape","shape-5"],[1,"hero-content"],[1,"hero-text"],[1,"greeting"],[1,"wave"],[1,"hero-name"],[1,"first-name"],[1,"last-name"],[1,"hero-title"],[1,"typing-text"],[1,"cursor"],[1,"hero-description"],[1,"hero-buttons"],["href","#contact",1,"btn","btn-primary"],[1,"fas","fa-arrow-right"],["href","#projects",1,"btn","btn-secondary"],[1,"fas","fa-eye"],[1,"hero-stats"],[1,"stat"],[1,"stat-number"],[1,"stat-label"],[1,"hero-image"],[1,"image-container"],[1,"image-background"],[1,"profile-placeholder"],[1,"initials"],[1,"image-overlay"],[1,"tech-icons"],[1,"tech-icon","tech-icon-1"],[1,"fab","fa-angular"],[1,"tech-icon","tech-icon-2"],[1,"fab","fa-java"],[1,"tech-icon","tech-icon-3"],[1,"fab","fa-js-square"],[1,"tech-icon","tech-icon-4"],[1,"fab","fa-html5"],[1,"tech-icon","tech-icon-5"],[1,"fab","fa-css3-alt"],[1,"tech-icon","tech-icon-6"],[1,"fab","fa-github"],[1,"scroll-indicator"],[1,"scroll-text"],[1,"scroll-arrow"],[1,"fas","fa-chevron-down"]],template:function(n,r){n&1&&(g(0,"div",0)(1,"div",1)(2,"div",2),V(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6)(7,"div",7),h()(),g(8,"div",8)(9,"div",9)(10,"div",10)(11,"span",11),D(12,"\u{1F44B}"),h(),g(13,"span"),D(14,"Hello, I'm"),h()(),g(15,"h1",12)(16,"span",13),D(17,"Sachin"),h(),g(18,"span",14),D(19,"Prabashwara"),h()(),g(20,"div",15)(21,"span",16),D(22),h(),g(23,"span",17),D(24,"|"),h()(),g(25,"p",18),D(26," Passionate Full Stack Developer Trainee specializing in modern web technologies, object-oriented programming, and creating exceptional user experiences. "),h(),g(27,"div",19)(28,"a",20)(29,"span"),D(30,"Get In Touch"),h(),V(31,"i",21),h(),g(32,"a",22)(33,"span"),D(34,"View My Work"),h(),V(35,"i",23),h()(),g(36,"div",24)(37,"div",25)(38,"span",26),D(39,"4+"),h(),g(40,"span",27),D(41,"Projects"),h()(),g(42,"div",25)(43,"span",26),D(44,"2+"),h(),g(45,"span",27),D(46,"Years Learning"),h()(),g(47,"div",25)(48,"span",26),D(49,"10+"),h(),g(50,"span",27),D(51,"Technologies"),h()()()(),g(52,"div",28)(53,"div",29),V(54,"div",30),g(55,"div",31)(56,"span",32),D(57,"SP"),h()(),V(58,"div",33),h(),g(59,"div",34)(60,"div",35),V(61,"i",36),h(),g(62,"div",37),V(63,"i",38),h(),g(64,"div",39),V(65,"i",40),h(),g(66,"div",41),V(67,"i",42),h(),g(68,"div",43),V(69,"i",44),h(),g(70,"div",45),V(71,"i",46),h()()()(),g(72,"div",47)(73,"div",48),D(74,"Scroll Down"),h(),g(75,"div",49),V(76,"i",50),h()()()),n&2&&(Re(10),Oe("@fadeInUp",void 0),Re(5),Oe("@fadeInUp",void 0),Re(5),Oe("@fadeInUp",void 0),Re(2),Ps(r.currentTitle),Re(3),Oe("@fadeInUp",void 0),Re(2),Oe("@fadeInUp",void 0),Re(9),Oe("@fadeInUp",void 0),Re(16),Oe("@fadeInRight",void 0),Re(20),Oe("@bounce",void 0))},styles:[".hero-container[_ngcontent-%COMP%]{position:relative;min-height:100vh;display:flex;align-items:center;justify-content:center;overflow:hidden;background:linear-gradient(135deg,#667eea,#764ba2)}.hero-background[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:1}.floating-shapes[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%}.shape[_ngcontent-%COMP%]{position:absolute;border-radius:50%;background:#ffffff1a;animation:_ngcontent-%COMP%_float 6s ease-in-out infinite}.shape-1[_ngcontent-%COMP%]{width:80px;height:80px;top:20%;left:10%;animation-delay:0s}.shape-2[_ngcontent-%COMP%]{width:120px;height:120px;top:60%;right:15%;animation-delay:2s}.shape-3[_ngcontent-%COMP%]{width:60px;height:60px;top:80%;left:20%;animation-delay:4s}.shape-4[_ngcontent-%COMP%]{width:100px;height:100px;top:30%;right:30%;animation-delay:1s}.shape-5[_ngcontent-%COMP%]{width:140px;height:140px;top:10%;right:5%;animation-delay:3s}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0) rotate(0)}50%{transform:translateY(-20px) rotate(180deg)}}.hero-content[_ngcontent-%COMP%]{position:relative;z-index:2;max-width:1200px;width:100%;padding:0 2rem;display:grid;grid-template-columns:1fr 1fr;gap:4rem;align-items:center}.hero-text[_ngcontent-%COMP%]{color:#fff}.greeting[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:1rem;display:flex;align-items:center;gap:.5rem}.wave[_ngcontent-%COMP%]{font-size:1.5rem;animation:_ngcontent-%COMP%_wave 2s ease-in-out infinite}@keyframes _ngcontent-%COMP%_wave{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-10deg)}}.hero-name[_ngcontent-%COMP%]{font-size:4rem;font-weight:700;margin-bottom:1rem;line-height:1.1}.first-name[_ngcontent-%COMP%]{display:block;background:linear-gradient(45deg,#fff,#f0f0f0);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.last-name[_ngcontent-%COMP%]{display:block;background:linear-gradient(45deg,gold,#ffed4e);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.hero-title[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:1.5rem;min-height:2rem}.typing-text[_ngcontent-%COMP%]{color:gold;font-weight:600}.cursor[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_blink 1s infinite;color:gold}@keyframes _ngcontent-%COMP%_blink{0%,50%{opacity:1}51%,to{opacity:0}}.hero-description[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.6;margin-bottom:2rem;opacity:.9}.hero-buttons[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:3rem}.btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:1rem 2rem;text-decoration:none;border-radius:50px;font-weight:600;transition:all .3s ease;position:relative;overflow:hidden}.btn-primary[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.btn-primary[_ngcontent-%COMP%]:hover{background:#ffffff4d;transform:translateY(-2px);box-shadow:0 10px 30px #0003}.btn-secondary[_ngcontent-%COMP%]{background:transparent;color:#fff;border:2px solid rgba(255,255,255,.5)}.btn-secondary[_ngcontent-%COMP%]:hover{background:#ffffff1a;transform:translateY(-2px)}.hero-stats[_ngcontent-%COMP%]{display:flex;gap:2rem}.stat[_ngcontent-%COMP%]{text-align:center}.stat-number[_ngcontent-%COMP%]{display:block;font-size:2rem;font-weight:700;color:gold}.stat-label[_ngcontent-%COMP%]{font-size:.9rem;opacity:.8}.hero-image[_ngcontent-%COMP%]{position:relative;display:flex;justify-content:center;align-items:center}.image-container[_ngcontent-%COMP%]{position:relative;width:400px;height:400px;border-radius:50%;overflow:hidden;box-shadow:0 20px 60px #0000004d}.image-background[_ngcontent-%COMP%]{position:absolute;inset:-10px;background:linear-gradient(45deg,gold,#ffed4e,#fff);border-radius:50%;animation:_ngcontent-%COMP%_rotate 10s linear infinite}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.profile-placeholder[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:50%;display:flex;align-items:center;justify-content:center;z-index:2}.initials[_ngcontent-%COMP%]{font-size:8rem;font-weight:700;color:#fff;text-shadow:2px 2px 4px rgba(0,0,0,.3)}.image-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(45deg,#667eea33,#764ba233);border-radius:50%;z-index:3}.tech-icons[_ngcontent-%COMP%]{position:absolute;width:500px;height:500px}.tech-icon[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;background:#ffffff1a;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.5rem;color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);animation:_ngcontent-%COMP%_float 4s ease-in-out infinite}.tech-icon-1[_ngcontent-%COMP%]{top:10%;left:20%;animation-delay:0s;color:#dd0031}.tech-icon-2[_ngcontent-%COMP%]{top:20%;right:10%;animation-delay:1s;color:#f89820}.tech-icon-3[_ngcontent-%COMP%]{bottom:30%;left:10%;animation-delay:2s;color:#f7df1e}.tech-icon-4[_ngcontent-%COMP%]{bottom:20%;right:20%;animation-delay:3s;color:#e34f26}.tech-icon-5[_ngcontent-%COMP%]{top:50%;left:5%;animation-delay:4s;color:#1572b6}.tech-icon-6[_ngcontent-%COMP%]{top:50%;right:5%;animation-delay:5s;color:#333}.scroll-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2rem;left:50%;transform:translate(-50%);text-align:center;color:#fffc;animation:_ngcontent-%COMP%_bounce 2s infinite}.scroll-text[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:.5rem}.scroll-arrow[_ngcontent-%COMP%]{font-size:1.2rem}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translate(-50%) translateY(0)}40%{transform:translate(-50%) translateY(-10px)}60%{transform:translate(-50%) translateY(-5px)}}@media (max-width: 768px){.hero-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:2rem;text-align:center}.hero-name[_ngcontent-%COMP%]{font-size:2.5rem}.hero-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.hero-stats[_ngcontent-%COMP%]{justify-content:center}.image-container[_ngcontent-%COMP%]{width:300px;height:300px}.tech-icons[_ngcontent-%COMP%]{width:350px;height:350px}.tech-icon[_ngcontent-%COMP%]{width:50px;height:50px;font-size:1.2rem}}"],data:{animation:[Kn("fadeInUp",[Xn(":enter",[ze({opacity:0,transform:"translateY(30px)"}),Jn("0.8s ease-out",ze({opacity:1,transform:"translateY(0)"}))])]),Kn("fadeInRight",[Xn(":enter",[ze({opacity:0,transform:"translateX(50px)"}),Jn("1s ease-out",ze({opacity:1,transform:"translateX(0)"}))])]),Kn("bounce",[Xn(":enter",[Jn("2s ease-in-out",xa([ze({transform:"translateY(0)",offset:0}),ze({transform:"translateY(-10px)",offset:.5}),ze({transform:"translateY(0)",offset:1})]))])])]}})};var Bo=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-about"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"about works!"),h())},encapsulation:2})};var zo=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-skills"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"skills works!"),h())},encapsulation:2})};var qo=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-education"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"education works!"),h())},encapsulation:2})};var Wo=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-experience"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"experience works!"),h())},encapsulation:2})};var Go=class e{static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-contact"]],decls:2,vars:0,template:function(n,r){n&1&&(g(0,"p"),D(1,"contact works!"),h())},encapsulation:2})};var Zo=class e{title="Sachin Prabashwara - Full Stack Developer";static \u0275fac=function(n){return new(n||e)};static \u0275cmp=z({type:e,selectors:[["app-root"]],decls:55,vars:0,consts:[[1,"navbar"],[1,"nav-container"],[1,"nav-logo"],[1,"nav-menu"],[1,"nav-item"],["href","#hero",1,"nav-link"],["href","#about",1,"nav-link"],["href","#skills",1,"nav-link"],["href","#education",1,"nav-link"],["href","#projects",1,"nav-link"],["href","#experience",1,"nav-link"],["href","#contact",1,"nav-link"],[1,"hamburger"],[1,"bar"],["id","hero"],["id","about"],["id","skills"],["id","education"],["id","projects"],["id","experience"],["id","contact"],[1,"footer"],[1,"footer-content"],[1,"social-links"],["href","https://www.linkedin.com/in/sachin-samarawickrama-349649312/","target","_blank",1,"social-link"],[1,"fab","fa-linkedin"],["href","https://github.com/sachinprabashwara4","target","_blank",1,"social-link"],[1,"fab","fa-github"]],template:function(n,r){n&1&&(g(0,"nav",0)(1,"div",1)(2,"div",2)(3,"span"),D(4,"Sachin Prabashwara"),h()(),g(5,"ul",3)(6,"li",4)(7,"a",5),D(8,"Home"),h()(),g(9,"li",4)(10,"a",6),D(11,"About"),h()(),g(12,"li",4)(13,"a",7),D(14,"Skills"),h()(),g(15,"li",4)(16,"a",8),D(17,"Education"),h()(),g(18,"li",4)(19,"a",9),D(20,"Projects"),h()(),g(21,"li",4)(22,"a",10),D(23,"Experience"),h()(),g(24,"li",4)(25,"a",11),D(26,"Contact"),h()()(),g(27,"div",12),V(28,"span",13)(29,"span",13)(30,"span",13),h()()(),g(31,"main")(32,"section",14),V(33,"app-hero"),h(),g(34,"section",15),V(35,"app-about"),h(),g(36,"section",16),V(37,"app-skills"),h(),g(38,"section",17),V(39,"app-education"),h(),g(40,"section",18),V(41,"app-projects"),h(),g(42,"section",19),V(43,"app-experience"),h(),g(44,"section",20),V(45,"app-contact"),h()(),g(46,"footer",21)(47,"div",22)(48,"p"),D(49,"\xA9 2024 Sachin Prabashwara. All rights reserved."),h(),g(50,"div",23)(51,"a",24),V(52,"i",25),h(),g(53,"a",26),V(54,"i",27),h()()()())},dependencies:[$o,Bo,zo,qo,sn,Wo,Go],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}body[_ngcontent-%COMP%]{font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif;line-height:1.6;color:#333;overflow-x:hidden}.navbar[_ngcontent-%COMP%]{position:fixed;top:0;width:100%;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:1000;padding:1rem 0;transition:all .3s ease;box-shadow:0 2px 20px #0000001a}.nav-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 2rem;display:flex;justify-content:space-between;align-items:center}.nav-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;background:linear-gradient(45deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.nav-menu[_ngcontent-%COMP%]{display:flex;list-style:none;gap:2rem}.nav-link[_ngcontent-%COMP%]{text-decoration:none;color:#333;font-weight:500;transition:all .3s ease;position:relative}.nav-link[_ngcontent-%COMP%]:hover{color:#667eea}.nav-link[_ngcontent-%COMP%]:after{content:"";position:absolute;width:0;height:2px;bottom:-5px;left:0;background:linear-gradient(45deg,#667eea,#764ba2);transition:width .3s ease}.nav-link[_ngcontent-%COMP%]:hover:after{width:100%}.hamburger[_ngcontent-%COMP%]{display:none;flex-direction:column;cursor:pointer}.bar[_ngcontent-%COMP%]{width:25px;height:3px;background:#333;margin:3px 0;transition:.3s}main[_ngcontent-%COMP%]{margin-top:80px}section[_ngcontent-%COMP%]{min-height:100vh;padding:2rem 0}.footer[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:2rem 0;text-align:center}.footer-content[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 2rem}.social-links[_ngcontent-%COMP%]{margin-top:1rem;display:flex;justify-content:center;gap:1rem}.social-link[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem;transition:all .3s ease;padding:.5rem;border-radius:50%;background:#ffffff1a}.social-link[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-3px)}@media (max-width: 768px){.nav-menu[_ngcontent-%COMP%]{position:fixed;left:-100%;top:70px;flex-direction:column;background-color:#fff;width:100%;text-align:center;transition:.3s;box-shadow:0 10px 27px #0000000d;padding:2rem 0}.nav-menu.active[_ngcontent-%COMP%]{left:0}.hamburger[_ngcontent-%COMP%]{display:flex}.nav-container[_ngcontent-%COMP%]{padding:0 1rem}}']})};Gs(Zo,Cf).catch(e=>console.error(e));
