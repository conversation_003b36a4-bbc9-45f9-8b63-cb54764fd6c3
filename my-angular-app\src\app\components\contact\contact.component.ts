import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}

@Component({
  selector: 'app-contact',
  imports: [FormsModule, CommonModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.css'
})
export class ContactComponent {
  formData: ContactForm = {
    name: '',
    email: '',
    subject: '',
    message: ''
  };

  onSubmit() {
    if (this.isFormValid()) {
      // Create mailto link with form data
      const subject = encodeURIComponent(this.formData.subject);
      const body = encodeURIComponent(
        `Name: ${this.formData.name}\n` +
        `Email: ${this.formData.email}\n\n` +
        `Message:\n${this.formData.message}`
      );

      const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
      window.open(mailtoLink);

      // Reset form
      this.resetForm();

      // Show success message (you can implement a toast notification here)
      alert('Thank you for your message! Your email client should open now.');
    }
  }

  private isFormValid(): boolean {
    return !!(this.formData.name &&
             this.formData.email &&
             this.formData.subject &&
             this.formData.message);
  }

  private resetForm() {
    this.formData = {
      name: '',
      email: '',
      subject: '',
      message: ''
    };
  }
}
