/* Education Container */
.education-container {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.education-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="edu-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1.5" fill="%23667eea" opacity="0.1"/><circle cx="10" cy="10" r="1" fill="%23764ba2" opacity="0.1"/><circle cx="30" cy="30" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23edu-pattern)"/></svg>');
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #666;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* Education Content */
.education-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Timeline */
.timeline {
  position: relative;
  padding: 2rem 0;
}

.timeline-line {
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 80px;
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInLeft 0.8s ease-out forwards;
}

.timeline-item:nth-child(2) {
  animation-delay: 0.2s;
}

.timeline-item:nth-child(3) {
  animation-delay: 0.4s;
}

.timeline-item:nth-child(4) {
  animation-delay: 0.6s;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Timeline Marker */
.timeline-marker {
  position: absolute;
  left: -50px;
  top: 20px;
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.marker-inner {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

/* Education Card */
.education-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.education-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.education-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 2rem 2rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.degree-info h3 {
  font-size: 1.4rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.degree-info h4 {
  font-size: 1.1rem;
  color: #667eea;
  font-weight: 500;
}

.year-badge {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
}

.card-body {
  padding: 0 2rem 2rem;
}

.description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.subjects h5 {
  font-size: 1rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tag {
  background: #f8f9fa;
  color: #667eea;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.tag:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

/* Achievements */
.achievements {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  font-size: 0.95rem;
  color: #666;
}

.achievement-item i {
  color: #ffd700;
  font-size: 1rem;
}

/* Education Stats */
.education-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.2rem;
}

.stat-info p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .education-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .education-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .timeline-item {
    padding-left: 60px;
  }

  .timeline-marker {
    left: -40px;
    width: 50px;
    height: 50px;
  }

  .marker-inner {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .timeline-line {
    left: 25px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .year-badge {
    align-self: flex-start;
  }

  .education-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .timeline-item {
    padding-left: 40px;
  }

  .timeline-marker {
    left: -30px;
    width: 40px;
    height: 40px;
  }

  .marker-inner {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .timeline-line {
    left: 20px;
  }

  .education-card {
    margin-left: -10px;
  }

  .card-header,
  .card-body {
    padding: 1.5rem;
  }
}