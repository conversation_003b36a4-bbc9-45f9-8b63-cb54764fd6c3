var ce=globalThis;function te(t){return(ce.__Zone_symbol_prefix||"__zone_symbol__")+t}function ht(){let t=ce.performance;function n(I){t&&t.mark&&t.mark(I)}function a(I,s){t&&t.measure&&t.measure(I,s)}n("Zone");class e{static __symbol__=te;static assertZonePatched(){if(ce.Promise!==S.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let s=e.current;for(;s.parent;)s=s.parent;return s}static get current(){return b.zone}static get currentTask(){return D}static __load_patch(s,i,r=!1){if(S.hasOwnProperty(s)){let E=ce[te("forceDuplicateZoneCheck")]===!0;if(!r&&E)throw Error("Already loaded patch: "+s)}else if(!ce["__Zone_disable_"+s]){let E="Zone:"+s;n(E),S[s]=i(ce,e,R),a(E,E)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(s,i){this._parent=s,this._name=i?i.name||"unnamed":"<root>",this._properties=i&&i.properties||{},this._zoneDelegate=new f(this,this._parent&&this._parent._zoneDelegate,i)}get(s){let i=this.getZoneWith(s);if(i)return i._properties[s]}getZoneWith(s){let i=this;for(;i;){if(i._properties.hasOwnProperty(s))return i;i=i._parent}return null}fork(s){if(!s)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,s)}wrap(s,i){if(typeof s!="function")throw new Error("Expecting function got: "+s);let r=this._zoneDelegate.intercept(this,s,i),E=this;return function(){return E.runGuarded(r,this,arguments,i)}}run(s,i,r,E){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,s,i,r,E)}finally{b=b.parent}}runGuarded(s,i=null,r,E){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,s,i,r,E)}catch(x){if(this._zoneDelegate.handleError(this,x))throw x}}finally{b=b.parent}}runTask(s,i,r){if(s.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(s.zone||J).name+"; Execution: "+this.name+")");let E=s,{type:x,data:{isPeriodic:ee=!1,isRefreshable:M=!1}={}}=s;if(s.state===q&&(x===U||x===k))return;let he=s.state!=A;he&&E._transitionTo(A,d);let _e=D;D=E,b={parent:b,zone:this};try{x==k&&s.data&&!ee&&!M&&(s.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,E,i,r)}catch(Q){if(this._zoneDelegate.handleError(this,Q))throw Q}}finally{let Q=s.state;if(Q!==q&&Q!==X)if(x==U||ee||M&&Q===p)he&&E._transitionTo(d,A,p);else{let Te=E._zoneDelegates;this._updateTaskCount(E,-1),he&&E._transitionTo(q,A,q),M&&(E._zoneDelegates=Te)}b=b.parent,D=_e}}scheduleTask(s){if(s.zone&&s.zone!==this){let r=this;for(;r;){if(r===s.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${s.zone.name}`);r=r.parent}}s._transitionTo(p,q);let i=[];s._zoneDelegates=i,s._zone=this;try{s=this._zoneDelegate.scheduleTask(this,s)}catch(r){throw s._transitionTo(X,p,q),this._zoneDelegate.handleError(this,r),r}return s._zoneDelegates===i&&this._updateTaskCount(s,1),s.state==p&&s._transitionTo(d,p),s}scheduleMicroTask(s,i,r,E){return this.scheduleTask(new g(F,s,i,r,E,void 0))}scheduleMacroTask(s,i,r,E,x){return this.scheduleTask(new g(k,s,i,r,E,x))}scheduleEventTask(s,i,r,E,x){return this.scheduleTask(new g(U,s,i,r,E,x))}cancelTask(s){if(s.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(s.zone||J).name+"; Execution: "+this.name+")");if(!(s.state!==d&&s.state!==A)){s._transitionTo(V,d,A);try{this._zoneDelegate.cancelTask(this,s)}catch(i){throw s._transitionTo(X,V),this._zoneDelegate.handleError(this,i),i}return this._updateTaskCount(s,-1),s._transitionTo(q,V),s.runCount=-1,s}}_updateTaskCount(s,i){let r=s._zoneDelegates;i==-1&&(s._zoneDelegates=null);for(let E=0;E<r.length;E++)r[E]._updateTaskCount(s.type,i)}}let c={name:"",onHasTask:(I,s,i,r)=>I.hasTask(i,r),onScheduleTask:(I,s,i,r)=>I.scheduleTask(i,r),onInvokeTask:(I,s,i,r,E,x)=>I.invokeTask(i,r,E,x),onCancelTask:(I,s,i,r)=>I.cancelTask(i,r)};class f{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(s,i,r){this._zone=s,this._parentDelegate=i,this._forkZS=r&&(r&&r.onFork?r:i._forkZS),this._forkDlgt=r&&(r.onFork?i:i._forkDlgt),this._forkCurrZone=r&&(r.onFork?this._zone:i._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:i._interceptZS),this._interceptDlgt=r&&(r.onIntercept?i:i._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this._zone:i._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:i._invokeZS),this._invokeDlgt=r&&(r.onInvoke?i:i._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this._zone:i._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:i._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?i:i._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this._zone:i._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:i._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?i:i._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this._zone:i._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:i._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?i:i._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this._zone:i._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:i._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?i:i._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this._zone:i._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let E=r&&r.onHasTask,x=i&&i._hasTaskZS;(E||x)&&(this._hasTaskZS=E?r:c,this._hasTaskDlgt=i,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,r.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=i,this._scheduleTaskCurrZone=this._zone),r.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=i,this._invokeTaskCurrZone=this._zone),r.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=i,this._cancelTaskCurrZone=this._zone))}fork(s,i){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,s,i):new e(s,i)}intercept(s,i,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,s,i,r):i}invoke(s,i,r,E,x){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,s,i,r,E,x):i.apply(r,E)}handleError(s,i){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,s,i):!0}scheduleTask(s,i){let r=i;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,s,i),r||(r=i);else if(i.scheduleFn)i.scheduleFn(i);else if(i.type==F)z(i);else throw new Error("Task is missing scheduleFn.");return r}invokeTask(s,i,r,E){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,s,i,r,E):i.callback.apply(r,E)}cancelTask(s,i){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,s,i);else{if(!i.cancelFn)throw Error("Task is not cancelable");r=i.cancelFn(i)}return r}hasTask(s,i){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,s,i)}catch(r){this.handleError(s,r)}}_updateTaskCount(s,i){let r=this._taskCounts,E=r[s],x=r[s]=E+i;if(x<0)throw new Error("More tasks executed then were scheduled.");if(E==0||x==0){let ee={microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:s};this.hasTask(this._zone,ee)}}}class g{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(s,i,r,E,x,ee){if(this.type=s,this.source=i,this.data=E,this.scheduleFn=x,this.cancelFn=ee,!r)throw new Error("callback is not defined");this.callback=r;let M=this;s===U&&E&&E.useG?this.invoke=g.invokeTask:this.invoke=function(){return g.invokeTask.call(ce,M,this,arguments)}}static invokeTask(s,i,r){s||(s=this),K++;try{return s.runCount++,s.zone.runTask(s,i,r)}finally{K==1&&$(),K--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(q,p)}_transitionTo(s,i,r){if(this._state===i||this._state===r)this._state=s,s==q&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${s}', expecting state '${i}'${r?" or '"+r+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let T=te("setTimeout"),y=te("Promise"),w=te("then"),_=[],P=!1,L;function H(I){if(L||ce[y]&&(L=ce[y].resolve(0)),L){let s=L[w];s||(s=L.then),s.call(L,I)}else ce[T](I,0)}function z(I){K===0&&_.length===0&&H($),I&&_.push(I)}function $(){if(!P){for(P=!0;_.length;){let I=_;_=[];for(let s=0;s<I.length;s++){let i=I[s];try{i.zone.runTask(i,null,null)}catch(r){R.onUnhandledError(r)}}}R.microtaskDrainDone(),P=!1}}let J={name:"NO ZONE"},q="notScheduled",p="scheduling",d="scheduled",A="running",V="canceling",X="unknown",F="microTask",k="macroTask",U="eventTask",S={},R={symbol:te,currentZoneFrame:()=>b,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:z,showUncaughtError:()=>!e[te("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:H},b={parent:null,zone:new e(null,null)},D=null,K=0;function W(){}return a("Zone","Zone"),e}function dt(){let t=globalThis,n=t[te("forceDuplicateZoneCheck")]===!0;if(t.Zone&&(n||typeof t.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return t.Zone??=ht(),t.Zone}var pe=Object.getOwnPropertyDescriptor,Me=Object.defineProperty,Ae=Object.getPrototypeOf,_t=Object.create,Tt=Array.prototype.slice,je="addEventListener",He="removeEventListener",Ne=te(je),Ze=te(He),ae="true",le="false",ve=te("");function Ve(t,n){return Zone.current.wrap(t,n)}function xe(t,n,a,e,c){return Zone.current.scheduleMacroTask(t,n,a,e,c)}var j=te,we=typeof window<"u",be=we?window:void 0,Y=we&&be||globalThis,Et="removeAttribute";function Fe(t,n){for(let a=t.length-1;a>=0;a--)typeof t[a]=="function"&&(t[a]=Ve(t[a],n+"_"+a));return t}function gt(t,n){let a=t.constructor.name;for(let e=0;e<n.length;e++){let c=n[e],f=t[c];if(f){let g=pe(t,c);if(!et(g))continue;t[c]=(T=>{let y=function(){return T.apply(this,Fe(arguments,a+"."+c))};return fe(y,T),y})(f)}}}function et(t){return t?t.writable===!1?!1:!(typeof t.get=="function"&&typeof t.set>"u"):!0}var tt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,De=!("nw"in Y)&&typeof Y.process<"u"&&Y.process.toString()==="[object process]",Ge=!De&&!tt&&!!(we&&be.HTMLElement),nt=typeof Y.process<"u"&&Y.process.toString()==="[object process]"&&!tt&&!!(we&&be.HTMLElement),Ce={},kt=j("enable_beforeunload"),Xe=function(t){if(t=t||Y.event,!t)return;let n=Ce[t.type];n||(n=Ce[t.type]=j("ON_PROPERTY"+t.type));let a=this||t.target||Y,e=a[n],c;if(Ge&&a===be&&t.type==="error"){let f=t;c=e&&e.call(this,f.message,f.filename,f.lineno,f.colno,f.error),c===!0&&t.preventDefault()}else c=e&&e.apply(this,arguments),t.type==="beforeunload"&&Y[kt]&&typeof c=="string"?t.returnValue=c:c!=null&&!c&&t.preventDefault();return c};function Ye(t,n,a){let e=pe(t,n);if(!e&&a&&pe(a,n)&&(e={enumerable:!0,configurable:!0}),!e||!e.configurable)return;let c=j("on"+n+"patched");if(t.hasOwnProperty(c)&&t[c])return;delete e.writable,delete e.value;let f=e.get,g=e.set,T=n.slice(2),y=Ce[T];y||(y=Ce[T]=j("ON_PROPERTY"+T)),e.set=function(w){let _=this;if(!_&&t===Y&&(_=Y),!_)return;typeof _[y]=="function"&&_.removeEventListener(T,Xe),g?.call(_,null),_[y]=w,typeof w=="function"&&_.addEventListener(T,Xe,!1)},e.get=function(){let w=this;if(!w&&t===Y&&(w=Y),!w)return null;let _=w[y];if(_)return _;if(f){let P=f.call(this);if(P)return e.set.call(this,P),typeof w[Et]=="function"&&w.removeAttribute(n),P}return null},Me(t,n,e),t[c]=!0}function rt(t,n,a){if(n)for(let e=0;e<n.length;e++)Ye(t,"on"+n[e],a);else{let e=[];for(let c in t)c.slice(0,2)=="on"&&e.push(c);for(let c=0;c<e.length;c++)Ye(t,e[c],a)}}var oe=j("originalInstance");function ye(t){let n=Y[t];if(!n)return;Y[j(t)]=n,Y[t]=function(){let c=Fe(arguments,t);switch(c.length){case 0:this[oe]=new n;break;case 1:this[oe]=new n(c[0]);break;case 2:this[oe]=new n(c[0],c[1]);break;case 3:this[oe]=new n(c[0],c[1],c[2]);break;case 4:this[oe]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},fe(Y[t],n);let a=new n(function(){}),e;for(e in a)t==="XMLHttpRequest"&&e==="responseBlob"||function(c){typeof a[c]=="function"?Y[t].prototype[c]=function(){return this[oe][c].apply(this[oe],arguments)}:Me(Y[t].prototype,c,{set:function(f){typeof f=="function"?(this[oe][c]=Ve(f,t+"."+c),fe(this[oe][c],f)):this[oe][c]=f},get:function(){return this[oe][c]}})}(e);for(e in n)e!=="prototype"&&n.hasOwnProperty(e)&&(Y[t][e]=n[e])}function ue(t,n,a){let e=t;for(;e&&!e.hasOwnProperty(n);)e=Ae(e);!e&&t[n]&&(e=t);let c=j(n),f=null;if(e&&(!(f=e[c])||!e.hasOwnProperty(c))){f=e[c]=e[n];let g=e&&pe(e,n);if(et(g)){let T=a(f,c,n);e[n]=function(){return T(this,arguments)},fe(e[n],f)}}return f}function mt(t,n,a){let e=null;function c(f){let g=f.data;return g.args[g.cbIdx]=function(){f.invoke.apply(this,arguments)},e.apply(g.target,g.args),f}e=ue(t,n,f=>function(g,T){let y=a(g,T);return y.cbIdx>=0&&typeof T[y.cbIdx]=="function"?xe(y.name,T[y.cbIdx],y,c):f.apply(g,T)})}function fe(t,n){t[j("OriginalDelegate")]=n}var $e=!1,Le=!1;function yt(){if($e)return Le;$e=!0;try{let t=be.navigator.userAgent;(t.indexOf("MSIE ")!==-1||t.indexOf("Trident/")!==-1||t.indexOf("Edge/")!==-1)&&(Le=!0)}catch{}return Le}function Je(t){return typeof t=="function"}function Ke(t){return typeof t=="number"}var pt={useG:!0},ne={},ot={},st=new RegExp("^"+ve+"(\\w+)(true|false)$"),it=j("propagationStopped");function ct(t,n){let a=(n?n(t):t)+le,e=(n?n(t):t)+ae,c=ve+a,f=ve+e;ne[t]={},ne[t][le]=c,ne[t][ae]=f}function vt(t,n,a,e){let c=e&&e.add||je,f=e&&e.rm||He,g=e&&e.listeners||"eventListeners",T=e&&e.rmAll||"removeAllListeners",y=j(c),w="."+c+":",_="prependListener",P="."+_+":",L=function(p,d,A){if(p.isRemoved)return;let V=p.callback;typeof V=="object"&&V.handleEvent&&(p.callback=k=>V.handleEvent(k),p.originalDelegate=V);let X;try{p.invoke(p,d,[A])}catch(k){X=k}let F=p.options;if(F&&typeof F=="object"&&F.once){let k=p.originalDelegate?p.originalDelegate:p.callback;d[f].call(d,A.type,k,F)}return X};function H(p,d,A){if(d=d||t.event,!d)return;let V=p||d.target||t,X=V[ne[d.type][A?ae:le]];if(X){let F=[];if(X.length===1){let k=L(X[0],V,d);k&&F.push(k)}else{let k=X.slice();for(let U=0;U<k.length&&!(d&&d[it]===!0);U++){let S=L(k[U],V,d);S&&F.push(S)}}if(F.length===1)throw F[0];for(let k=0;k<F.length;k++){let U=F[k];n.nativeScheduleMicroTask(()=>{throw U})}}}let z=function(p){return H(this,p,!1)},$=function(p){return H(this,p,!0)};function J(p,d){if(!p)return!1;let A=!0;d&&d.useG!==void 0&&(A=d.useG);let V=d&&d.vh,X=!0;d&&d.chkDup!==void 0&&(X=d.chkDup);let F=!1;d&&d.rt!==void 0&&(F=d.rt);let k=p;for(;k&&!k.hasOwnProperty(c);)k=Ae(k);if(!k&&p[c]&&(k=p),!k||k[y])return!1;let U=d&&d.eventNameToString,S={},R=k[y]=k[c],b=k[j(f)]=k[f],D=k[j(g)]=k[g],K=k[j(T)]=k[T],W;d&&d.prepend&&(W=k[j(d.prepend)]=k[d.prepend]);function I(o,u){return u?typeof o=="boolean"?{capture:o,passive:!0}:o?typeof o=="object"&&o.passive!==!1?{...o,passive:!0}:o:{passive:!0}:o}let s=function(o){if(!S.isExisting)return R.call(S.target,S.eventName,S.capture?$:z,S.options)},i=function(o){if(!o.isRemoved){let u=ne[o.eventName],v;u&&(v=u[o.capture?ae:le]);let C=v&&o.target[v];if(C){for(let m=0;m<C.length;m++)if(C[m]===o){C.splice(m,1),o.isRemoved=!0,o.removeAbortListener&&(o.removeAbortListener(),o.removeAbortListener=null),C.length===0&&(o.allRemoved=!0,o.target[v]=null);break}}}if(o.allRemoved)return b.call(o.target,o.eventName,o.capture?$:z,o.options)},r=function(o){return R.call(S.target,S.eventName,o.invoke,S.options)},E=function(o){return W.call(S.target,S.eventName,o.invoke,S.options)},x=function(o){return b.call(o.target,o.eventName,o.invoke,o.options)},ee=A?s:r,M=A?i:x,he=function(o,u){let v=typeof u;return v==="function"&&o.callback===u||v==="object"&&o.originalDelegate===u},_e=d?.diff||he,Q=Zone[j("UNPATCHED_EVENTS")],Te=t[j("PASSIVE_EVENTS")];function h(o){if(typeof o=="object"&&o!==null){let u={...o};return o.signal&&(u.signal=o.signal),u}return o}let l=function(o,u,v,C,m=!1,O=!1){return function(){let N=this||t,Z=arguments[0];d&&d.transferEventName&&(Z=d.transferEventName(Z));let G=arguments[1];if(!G)return o.apply(this,arguments);if(De&&Z==="uncaughtException")return o.apply(this,arguments);let B=!1;if(typeof G!="function"){if(!G.handleEvent)return o.apply(this,arguments);B=!0}if(V&&!V(o,G,N,arguments))return;let de=!!Te&&Te.indexOf(Z)!==-1,se=h(I(arguments[2],de)),Ee=se?.signal;if(Ee?.aborted)return;if(Q){for(let ie=0;ie<Q.length;ie++)if(Z===Q[ie])return de?o.call(N,Z,G,se):o.apply(this,arguments)}let Se=se?typeof se=="boolean"?!0:se.capture:!1,Be=se&&typeof se=="object"?se.once:!1,ft=Zone.current,Oe=ne[Z];Oe||(ct(Z,U),Oe=ne[Z]);let ze=Oe[Se?ae:le],ge=N[ze],Ue=!1;if(ge){if(Ue=!0,X){for(let ie=0;ie<ge.length;ie++)if(_e(ge[ie],G))return}}else ge=N[ze]=[];let Pe,We=N.constructor.name,qe=ot[We];qe&&(Pe=qe[Z]),Pe||(Pe=We+u+(U?U(Z):Z)),S.options=se,Be&&(S.options.once=!1),S.target=N,S.capture=Se,S.eventName=Z,S.isExisting=Ue;let me=A?pt:void 0;me&&(me.taskData=S),Ee&&(S.options.signal=void 0);let re=ft.scheduleEventTask(Pe,G,me,v,C);if(Ee){S.options.signal=Ee;let ie=()=>re.zone.cancelTask(re);o.call(Ee,"abort",ie,{once:!0}),re.removeAbortListener=()=>Ee.removeEventListener("abort",ie)}if(S.target=null,me&&(me.taskData=null),Be&&(S.options.once=!0),typeof re.options!="boolean"&&(re.options=se),re.target=N,re.capture=Se,re.eventName=Z,B&&(re.originalDelegate=G),O?ge.unshift(re):ge.push(re),m)return N}};return k[c]=l(R,w,ee,M,F),W&&(k[_]=l(W,P,E,M,F,!0)),k[f]=function(){let o=this||t,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=arguments[2],C=v?typeof v=="boolean"?!0:v.capture:!1,m=arguments[1];if(!m)return b.apply(this,arguments);if(V&&!V(b,m,o,arguments))return;let O=ne[u],N;O&&(N=O[C?ae:le]);let Z=N&&o[N];if(Z)for(let G=0;G<Z.length;G++){let B=Z[G];if(_e(B,m)){if(Z.splice(G,1),B.isRemoved=!0,Z.length===0&&(B.allRemoved=!0,o[N]=null,!C&&typeof u=="string")){let de=ve+"ON_PROPERTY"+u;o[de]=null}return B.zone.cancelTask(B),F?o:void 0}}return b.apply(this,arguments)},k[g]=function(){let o=this||t,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=[],C=at(o,U?U(u):u);for(let m=0;m<C.length;m++){let O=C[m],N=O.originalDelegate?O.originalDelegate:O.callback;v.push(N)}return v},k[T]=function(){let o=this||t,u=arguments[0];if(u){d&&d.transferEventName&&(u=d.transferEventName(u));let v=ne[u];if(v){let C=v[le],m=v[ae],O=o[C],N=o[m];if(O){let Z=O.slice();for(let G=0;G<Z.length;G++){let B=Z[G],de=B.originalDelegate?B.originalDelegate:B.callback;this[f].call(this,u,de,B.options)}}if(N){let Z=N.slice();for(let G=0;G<Z.length;G++){let B=Z[G],de=B.originalDelegate?B.originalDelegate:B.callback;this[f].call(this,u,de,B.options)}}}}else{let v=Object.keys(o);for(let C=0;C<v.length;C++){let m=v[C],O=st.exec(m),N=O&&O[1];N&&N!=="removeListener"&&this[T].call(this,N)}this[T].call(this,"removeListener")}if(F)return this},fe(k[c],R),fe(k[f],b),K&&fe(k[T],K),D&&fe(k[g],D),!0}let q=[];for(let p=0;p<a.length;p++)q[p]=J(a[p],e);return q}function at(t,n){if(!n){let f=[];for(let g in t){let T=st.exec(g),y=T&&T[1];if(y&&(!n||y===n)){let w=t[g];if(w)for(let _=0;_<w.length;_++)f.push(w[_])}}return f}let a=ne[n];a||(ct(n),a=ne[n]);let e=t[a[le]],c=t[a[ae]];return e?c?e.concat(c):e.slice():c?c.slice():[]}function bt(t,n){let a=t.Event;a&&a.prototype&&n.patchMethod(a.prototype,"stopImmediatePropagation",e=>function(c,f){c[it]=!0,e&&e.apply(c,f)})}function Pt(t,n){n.patchMethod(t,"queueMicrotask",a=>function(e,c){Zone.current.scheduleMicroTask("queueMicrotask",c[0])})}var Re=j("zoneTask");function ke(t,n,a,e){let c=null,f=null;n+=e,a+=e;let g={};function T(w){let _=w.data;_.args[0]=function(){return w.invoke.apply(this,arguments)};let P=c.apply(t,_.args);return Ke(P)?_.handleId=P:(_.handle=P,_.isRefreshable=Je(P.refresh)),w}function y(w){let{handle:_,handleId:P}=w.data;return f.call(t,_??P)}c=ue(t,n,w=>function(_,P){if(Je(P[0])){let L={isRefreshable:!1,isPeriodic:e==="Interval",delay:e==="Timeout"||e==="Interval"?P[1]||0:void 0,args:P},H=P[0];P[0]=function(){try{return H.apply(this,arguments)}finally{let{handle:A,handleId:V,isPeriodic:X,isRefreshable:F}=L;!X&&!F&&(V?delete g[V]:A&&(A[Re]=null))}};let z=xe(n,P[0],L,T,y);if(!z)return z;let{handleId:$,handle:J,isRefreshable:q,isPeriodic:p}=z.data;if($)g[$]=z;else if(J&&(J[Re]=z,q&&!p)){let d=J.refresh;J.refresh=function(){let{zone:A,state:V}=z;return V==="notScheduled"?(z._state="scheduled",A._updateTaskCount(z,1)):V==="running"&&(z._state="scheduling"),d.call(this)}}return J??$??z}else return w.apply(t,P)}),f=ue(t,a,w=>function(_,P){let L=P[0],H;Ke(L)?(H=g[L],delete g[L]):(H=L?.[Re],H?L[Re]=null:H=L),H?.type?H.cancelFn&&H.zone.cancelTask(H):w.apply(t,P)})}function Rt(t,n){let{isBrowser:a,isMix:e}=n.getGlobalObjects();if(!a&&!e||!t.customElements||!("customElements"in t))return;let c=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];n.patchCallbacks(n,t.customElements,"customElements","define",c)}function Ct(t,n){if(Zone[n.symbol("patchEventTarget")])return;let{eventNames:a,zoneSymbolEventNames:e,TRUE_STR:c,FALSE_STR:f,ZONE_SYMBOL_PREFIX:g}=n.getGlobalObjects();for(let y=0;y<a.length;y++){let w=a[y],_=w+f,P=w+c,L=g+_,H=g+P;e[w]={},e[w][f]=L,e[w][c]=H}let T=t.EventTarget;if(!(!T||!T.prototype))return n.patchEventTarget(t,n,[T&&T.prototype]),!0}function wt(t,n){n.patchEventPrototype(t,n)}function lt(t,n,a){if(!a||a.length===0)return n;let e=a.filter(f=>f.target===t);if(e.length===0)return n;let c=e[0].ignoreProperties;return n.filter(f=>c.indexOf(f)===-1)}function Qe(t,n,a,e){if(!t)return;let c=lt(t,n,a);rt(t,c,e)}function Ie(t){return Object.getOwnPropertyNames(t).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}function Dt(t,n){if(De&&!nt||Zone[t.symbol("patchEvents")])return;let a=n.__Zone_ignore_on_properties,e=[];if(Ge){let c=window;e=e.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let f=[];Qe(c,Ie(c),a&&a.concat(f),Ae(c))}e=e.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<e.length;c++){let f=n[e[c]];f?.prototype&&Qe(f.prototype,Ie(f.prototype),a)}}function St(t){t.__load_patch("legacy",n=>{let a=n[t.__symbol__("legacyPatch")];a&&a()}),t.__load_patch("timers",n=>{let a="set",e="clear";ke(n,a,e,"Timeout"),ke(n,a,e,"Interval"),ke(n,a,e,"Immediate")}),t.__load_patch("requestAnimationFrame",n=>{ke(n,"request","cancel","AnimationFrame"),ke(n,"mozRequest","mozCancel","AnimationFrame"),ke(n,"webkitRequest","webkitCancel","AnimationFrame")}),t.__load_patch("blocking",(n,a)=>{let e=["alert","prompt","confirm"];for(let c=0;c<e.length;c++){let f=e[c];ue(n,f,(g,T,y)=>function(w,_){return a.current.run(g,n,_,y)})}}),t.__load_patch("EventTarget",(n,a,e)=>{wt(n,e),Ct(n,e);let c=n.XMLHttpRequestEventTarget;c&&c.prototype&&e.patchEventTarget(n,e,[c.prototype])}),t.__load_patch("MutationObserver",(n,a,e)=>{ye("MutationObserver"),ye("WebKitMutationObserver")}),t.__load_patch("IntersectionObserver",(n,a,e)=>{ye("IntersectionObserver")}),t.__load_patch("FileReader",(n,a,e)=>{ye("FileReader")}),t.__load_patch("on_property",(n,a,e)=>{Dt(e,n)}),t.__load_patch("customElements",(n,a,e)=>{Rt(n,e)}),t.__load_patch("XHR",(n,a)=>{w(n);let e=j("xhrTask"),c=j("xhrSync"),f=j("xhrListener"),g=j("xhrScheduled"),T=j("xhrURL"),y=j("xhrErrorBeforeScheduled");function w(_){let P=_.XMLHttpRequest;if(!P)return;let L=P.prototype;function H(R){return R[e]}let z=L[Ne],$=L[Ze];if(!z){let R=_.XMLHttpRequestEventTarget;if(R){let b=R.prototype;z=b[Ne],$=b[Ze]}}let J="readystatechange",q="scheduled";function p(R){let b=R.data,D=b.target;D[g]=!1,D[y]=!1;let K=D[f];z||(z=D[Ne],$=D[Ze]),K&&$.call(D,J,K);let W=D[f]=()=>{if(D.readyState===D.DONE)if(!b.aborted&&D[g]&&R.state===q){let s=D[a.__symbol__("loadfalse")];if(D.status!==0&&s&&s.length>0){let i=R.invoke;R.invoke=function(){let r=D[a.__symbol__("loadfalse")];for(let E=0;E<r.length;E++)r[E]===R&&r.splice(E,1);!b.aborted&&R.state===q&&i.call(R)},s.push(R)}else R.invoke()}else!b.aborted&&D[g]===!1&&(D[y]=!0)};return z.call(D,J,W),D[e]||(D[e]=R),U.apply(D,b.args),D[g]=!0,R}function d(){}function A(R){let b=R.data;return b.aborted=!0,S.apply(b.target,b.args)}let V=ue(L,"open",()=>function(R,b){return R[c]=b[2]==!1,R[T]=b[1],V.apply(R,b)}),X="XMLHttpRequest.send",F=j("fetchTaskAborting"),k=j("fetchTaskScheduling"),U=ue(L,"send",()=>function(R,b){if(a.current[k]===!0||R[c])return U.apply(R,b);{let D={target:R,url:R[T],isPeriodic:!1,args:b,aborted:!1},K=xe(X,d,D,p,A);R&&R[y]===!0&&!D.aborted&&K.state===q&&K.invoke()}}),S=ue(L,"abort",()=>function(R,b){let D=H(R);if(D&&typeof D.type=="string"){if(D.cancelFn==null||D.data&&D.data.aborted)return;D.zone.cancelTask(D)}else if(a.current[F]===!0)return S.apply(R,b)})}}),t.__load_patch("geolocation",n=>{n.navigator&&n.navigator.geolocation&&gt(n.navigator.geolocation,["getCurrentPosition","watchPosition"])}),t.__load_patch("PromiseRejectionEvent",(n,a)=>{function e(c){return function(f){at(n,c).forEach(T=>{let y=n.PromiseRejectionEvent;if(y){let w=new y(c,{promise:f.promise,reason:f.rejection});T.invoke(w)}})}}n.PromiseRejectionEvent&&(a[j("unhandledPromiseRejectionHandler")]=e("unhandledrejection"),a[j("rejectionHandledHandler")]=e("rejectionhandled"))}),t.__load_patch("queueMicrotask",(n,a,e)=>{Pt(n,e)})}function Ot(t){t.__load_patch("ZoneAwarePromise",(n,a,e)=>{let c=Object.getOwnPropertyDescriptor,f=Object.defineProperty;function g(h){if(h&&h.toString===Object.prototype.toString){let l=h.constructor&&h.constructor.name;return(l||"")+": "+JSON.stringify(h)}return h?h.toString():Object.prototype.toString.call(h)}let T=e.symbol,y=[],w=n[T("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,_=T("Promise"),P=T("then"),L="__creationTrace__";e.onUnhandledError=h=>{if(e.showUncaughtError()){let l=h&&h.rejection;l?console.error("Unhandled Promise rejection:",l instanceof Error?l.message:l,"; Zone:",h.zone.name,"; Task:",h.task&&h.task.source,"; Value:",l,l instanceof Error?l.stack:void 0):console.error(h)}},e.microtaskDrainDone=()=>{for(;y.length;){let h=y.shift();try{h.zone.runGuarded(()=>{throw h.throwOriginal?h.rejection:h})}catch(l){z(l)}}};let H=T("unhandledPromiseRejectionHandler");function z(h){e.onUnhandledError(h);try{let l=a[H];typeof l=="function"&&l.call(this,h)}catch{}}function $(h){return h&&typeof h.then=="function"}function J(h){return h}function q(h){return M.reject(h)}let p=T("state"),d=T("value"),A=T("finally"),V=T("parentPromiseValue"),X=T("parentPromiseState"),F="Promise.then",k=null,U=!0,S=!1,R=0;function b(h,l){return o=>{try{I(h,l,o)}catch(u){I(h,!1,u)}}}let D=function(){let h=!1;return function(o){return function(){h||(h=!0,o.apply(null,arguments))}}},K="Promise resolved with itself",W=T("currentTaskTrace");function I(h,l,o){let u=D();if(h===o)throw new TypeError(K);if(h[p]===k){let v=null;try{(typeof o=="object"||typeof o=="function")&&(v=o&&o.then)}catch(C){return u(()=>{I(h,!1,C)})(),h}if(l!==S&&o instanceof M&&o.hasOwnProperty(p)&&o.hasOwnProperty(d)&&o[p]!==k)i(o),I(h,o[p],o[d]);else if(l!==S&&typeof v=="function")try{v.call(o,u(b(h,l)),u(b(h,!1)))}catch(C){u(()=>{I(h,!1,C)})()}else{h[p]=l;let C=h[d];if(h[d]=o,h[A]===A&&l===U&&(h[p]=h[X],h[d]=h[V]),l===S&&o instanceof Error){let m=a.currentTask&&a.currentTask.data&&a.currentTask.data[L];m&&f(o,W,{configurable:!0,enumerable:!1,writable:!0,value:m})}for(let m=0;m<C.length;)r(h,C[m++],C[m++],C[m++],C[m++]);if(C.length==0&&l==S){h[p]=R;let m=o;try{throw new Error("Uncaught (in promise): "+g(o)+(o&&o.stack?`
`+o.stack:""))}catch(O){m=O}w&&(m.throwOriginal=!0),m.rejection=o,m.promise=h,m.zone=a.current,m.task=a.currentTask,y.push(m),e.scheduleMicroTask()}}}return h}let s=T("rejectionHandledHandler");function i(h){if(h[p]===R){try{let l=a[s];l&&typeof l=="function"&&l.call(this,{rejection:h[d],promise:h})}catch{}h[p]=S;for(let l=0;l<y.length;l++)h===y[l].promise&&y.splice(l,1)}}function r(h,l,o,u,v){i(h);let C=h[p],m=C?typeof u=="function"?u:J:typeof v=="function"?v:q;l.scheduleMicroTask(F,()=>{try{let O=h[d],N=!!o&&A===o[A];N&&(o[V]=O,o[X]=C);let Z=l.run(m,void 0,N&&m!==q&&m!==J?[]:[O]);I(o,!0,Z)}catch(O){I(o,!1,O)}},o)}let E="function ZoneAwarePromise() { [native code] }",x=function(){},ee=n.AggregateError;class M{static toString(){return E}static resolve(l){return l instanceof M?l:I(new this(null),U,l)}static reject(l){return I(new this(null),S,l)}static withResolvers(){let l={};return l.promise=new M((o,u)=>{l.resolve=o,l.reject=u}),l}static any(l){if(!l||typeof l[Symbol.iterator]!="function")return Promise.reject(new ee([],"All promises were rejected"));let o=[],u=0;try{for(let m of l)u++,o.push(M.resolve(m))}catch{return Promise.reject(new ee([],"All promises were rejected"))}if(u===0)return Promise.reject(new ee([],"All promises were rejected"));let v=!1,C=[];return new M((m,O)=>{for(let N=0;N<o.length;N++)o[N].then(Z=>{v||(v=!0,m(Z))},Z=>{C.push(Z),u--,u===0&&(v=!0,O(new ee(C,"All promises were rejected")))})})}static race(l){let o,u,v=new this((O,N)=>{o=O,u=N});function C(O){o(O)}function m(O){u(O)}for(let O of l)$(O)||(O=this.resolve(O)),O.then(C,m);return v}static all(l){return M.allWithCallback(l)}static allSettled(l){return(this&&this.prototype instanceof M?this:M).allWithCallback(l,{thenCallback:u=>({status:"fulfilled",value:u}),errorCallback:u=>({status:"rejected",reason:u})})}static allWithCallback(l,o){let u,v,C=new this((Z,G)=>{u=Z,v=G}),m=2,O=0,N=[];for(let Z of l){$(Z)||(Z=this.resolve(Z));let G=O;try{Z.then(B=>{N[G]=o?o.thenCallback(B):B,m--,m===0&&u(N)},B=>{o?(N[G]=o.errorCallback(B),m--,m===0&&u(N)):v(B)})}catch(B){v(B)}m++,O++}return m-=2,m===0&&u(N),C}constructor(l){let o=this;if(!(o instanceof M))throw new Error("Must be an instanceof Promise.");o[p]=k,o[d]=[];try{let u=D();l&&l(u(b(o,U)),u(b(o,S)))}catch(u){I(o,!1,u)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return M}then(l,o){let u=this.constructor?.[Symbol.species];(!u||typeof u!="function")&&(u=this.constructor||M);let v=new u(x),C=a.current;return this[p]==k?this[d].push(C,v,l,o):r(this,C,v,l,o),v}catch(l){return this.then(null,l)}finally(l){let o=this.constructor?.[Symbol.species];(!o||typeof o!="function")&&(o=M);let u=new o(x);u[A]=A;let v=a.current;return this[p]==k?this[d].push(v,u,l,l):r(this,v,u,l,l),u}}M.resolve=M.resolve,M.reject=M.reject,M.race=M.race,M.all=M.all;let he=n[_]=n.Promise;n.Promise=M;let _e=T("thenPatched");function Q(h){let l=h.prototype,o=c(l,"then");if(o&&(o.writable===!1||!o.configurable))return;let u=l.then;l[P]=u,h.prototype.then=function(v,C){return new M((O,N)=>{u.call(this,O,N)}).then(v,C)},h[_e]=!0}e.patchThen=Q;function Te(h){return function(l,o){let u=h.apply(l,o);if(u instanceof M)return u;let v=u.constructor;return v[_e]||Q(v),u}}return he&&(Q(he),ue(n,"fetch",h=>Te(h))),Promise[a.__symbol__("uncaughtPromiseErrors")]=y,M})}function Nt(t){t.__load_patch("toString",n=>{let a=Function.prototype.toString,e=j("OriginalDelegate"),c=j("Promise"),f=j("Error"),g=function(){if(typeof this=="function"){let _=this[e];if(_)return typeof _=="function"?a.call(_):Object.prototype.toString.call(_);if(this===Promise){let P=n[c];if(P)return a.call(P)}if(this===Error){let P=n[f];if(P)return a.call(P)}}return a.call(this)};g[e]=a,Function.prototype.toString=g;let T=Object.prototype.toString,y="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?y:T.call(this)}})}function Zt(t,n,a,e,c){let f=Zone.__symbol__(e);if(n[f])return;let g=n[f]=n[e];n[e]=function(T,y,w){return y&&y.prototype&&c.forEach(function(_){let P=`${a}.${e}::`+_,L=y.prototype;try{if(L.hasOwnProperty(_)){let H=t.ObjectGetOwnPropertyDescriptor(L,_);H&&H.value?(H.value=t.wrapWithCurrentZone(H.value,P),t._redefineProperty(y.prototype,_,H)):L[_]&&(L[_]=t.wrapWithCurrentZone(L[_],P))}else L[_]&&(L[_]=t.wrapWithCurrentZone(L[_],P))}catch{}}),g.call(n,T,y,w)},t.attachOriginToPatched(n[e],g)}function Lt(t){t.__load_patch("util",(n,a,e)=>{let c=Ie(n);e.patchOnProperties=rt,e.patchMethod=ue,e.bindArguments=Fe,e.patchMacroTask=mt;let f=a.__symbol__("BLACK_LISTED_EVENTS"),g=a.__symbol__("UNPATCHED_EVENTS");n[g]&&(n[f]=n[g]),n[f]&&(a[f]=a[g]=n[f]),e.patchEventPrototype=bt,e.patchEventTarget=vt,e.isIEOrEdge=yt,e.ObjectDefineProperty=Me,e.ObjectGetOwnPropertyDescriptor=pe,e.ObjectCreate=_t,e.ArraySlice=Tt,e.patchClass=ye,e.wrapWithCurrentZone=Ve,e.filterProperties=lt,e.attachOriginToPatched=fe,e._redefineProperty=Object.defineProperty,e.patchCallbacks=Zt,e.getGlobalObjects=()=>({globalSources:ot,zoneSymbolEventNames:ne,eventNames:c,isBrowser:Ge,isMix:nt,isNode:De,TRUE_STR:ae,FALSE_STR:le,ZONE_SYMBOL_PREFIX:ve,ADD_EVENT_LISTENER_STR:je,REMOVE_EVENT_LISTENER_STR:He})})}function It(t){Ot(t),Nt(t),Lt(t)}var ut=dt();It(ut);St(ut);
