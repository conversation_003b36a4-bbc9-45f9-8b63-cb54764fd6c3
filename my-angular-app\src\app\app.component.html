<!-- Navigation Header -->
<nav class="navbar" [class.scrolled]="isScrolled">
  <div class="nav-container">
    <div class="nav-logo" (click)="scrollToSection('hero')">
      <span>Sachin Prabashwara</span>
    </div>
    <ul class="nav-menu" [class.active]="isMobileMenuOpen">
      <li class="nav-item">
        <a (click)="scrollToSection('hero')" class="nav-link">Home</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('about')" class="nav-link">About</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('skills')" class="nav-link">Skills</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('education')" class="nav-link">Education</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('projects')" class="nav-link">Projects</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('experience')" class="nav-link">Experience</a>
      </li>
      <li class="nav-item">
        <a (click)="scrollToSection('contact')" class="nav-link">Contact</a>
      </li>
    </ul>
    <div class="hamburger" [class.active]="isMobileMenuOpen" (click)="toggleMobileMenu()">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
  </div>
</nav>

<!-- Main Content Sections -->
<main>
  <section id="hero">
    <app-hero></app-hero>
  </section>

  <section id="about">
    <app-about></app-about>
  </section>

  <section id="skills">
    <app-skills></app-skills>
  </section>

  <section id="education">
    <app-education></app-education>
  </section>

  <section id="projects">
    <app-projects></app-projects>
  </section>

  <section id="experience">
    <app-experience></app-experience>
  </section>

  <section id="contact">
    <app-contact></app-contact>
  </section>
</main>

<!-- Footer -->
<footer class="footer">
  <div class="footer-content">
    <p>&copy; 2024 Sachin Prabashwara. All rights reserved.</p>
    <div class="social-links">
      <a href="https://www.linkedin.com/in/sachin-samarawickrama-349649312/" target="_blank" class="social-link">
        <i class="fab fa-linkedin"></i>
      </a>
      <a href="https://github.com/macamisp" target="_blank" class="social-link">
        <i class="fab fa-github"></i>
      </a>
      <a href="https://www.facebook.com/profile.php?id=100069446774540" target="_blank" class="social-link">
        <i class="fab fa-facebook"></i>
      </a>
      <a href="https://www.instagram.com/macami_impres/" target="_blank" class="social-link">
        <i class="fab fa-instagram"></i>
      </a>
      <a href="https://wa.me/94788179855" target="_blank" class="social-link">
        <i class="fab fa-whatsapp"></i>
      </a>
    </div>
  </div>
</footer>