{"version": 3, "file": "util-D9FfmVnv.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/error_helpers.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/web_animations/animatable_props_set.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/render/shared.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/animations/browser/src/util.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeErrorCode} from '../../src/errors';\nimport {ɵRuntimeError as RuntimeError} from '@angular/core';\n\nconst LINE_START = '\\n - ';\n\nexport function invalidTimingValue(exp: string | number): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TIMING_VALUE,\n    ngDevMode && `The provided timing value \"${exp}\" is invalid.`,\n  );\n}\n\nexport function negativeStepValue(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.NEGATIVE_STEP_VALUE,\n    ngDevMode && 'Duration values below 0 are not allowed for this animation step.',\n  );\n}\n\nexport function negativeDelayValue(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.NEGATIVE_DELAY_VALUE,\n    ngDevMode && 'Delay values below 0 are not allowed for this animation step.',\n  );\n}\n\nexport function invalidStyleParams(varName: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STYLE_PARAMS,\n    ngDevMode &&\n      `Unable to resolve the local animation param ${varName} in the given list of values`,\n  );\n}\n\nexport function invalidParamValue(varName: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PARAM_VALUE,\n    ngDevMode && `Please provide a value for the animation param ${varName}`,\n  );\n}\n\nexport function invalidNodeType(nodeType: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_NODE_TYPE,\n    ngDevMode && `Unable to resolve animation metadata node #${nodeType}`,\n  );\n}\n\nexport function invalidCssUnitValue(userProvidedProperty: string, value: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_CSS_UNIT_VALUE,\n    ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`,\n  );\n}\n\nexport function invalidTrigger(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TRIGGER,\n    ngDevMode &&\n      \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\",\n  );\n}\n\nexport function invalidDefinition(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_DEFINITION,\n    ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()',\n  );\n}\n\nexport function invalidState(metadataName: string, missingSubs: string[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STATE,\n    ngDevMode &&\n      `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(\n        ', ',\n      )}`,\n  );\n}\n\nexport function invalidStyleValue(value: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STYLE_VALUE,\n    ngDevMode && `The provided style string value ${value} is not allowed.`,\n  );\n}\n\nexport function invalidProperty(prop: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PROPERTY,\n    ngDevMode &&\n      `The provided animation property \"${prop}\" is not a supported CSS property for animations`,\n  );\n}\n\nexport function invalidParallelAnimation(\n  prop: string,\n  firstStart: number,\n  firstEnd: number,\n  secondStart: number,\n  secondEnd: number,\n): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_PARALLEL_ANIMATION,\n    ngDevMode &&\n      `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`,\n  );\n}\n\nexport function invalidKeyframes(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_KEYFRAMES,\n    ngDevMode && `keyframes() must be placed inside of a call to animate()`,\n  );\n}\n\nexport function invalidOffset(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_OFFSET,\n    ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`,\n  );\n}\n\nexport function keyframeOffsetsOutOfOrder(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER,\n    ngDevMode && `Please ensure that all keyframe offsets are in order`,\n  );\n}\n\nexport function keyframesMissingOffsets(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS,\n    ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`,\n  );\n}\n\nexport function invalidStagger(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_STAGGER,\n    ngDevMode && `stagger() can only be used inside of query()`,\n  );\n}\n\nexport function invalidQuery(selector: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_QUERY,\n    ngDevMode &&\n      `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`,\n  );\n}\n\nexport function invalidExpression(expr: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_EXPRESSION,\n    ngDevMode && `The provided transition expression \"${expr}\" is not supported`,\n  );\n}\n\nexport function invalidTransitionAlias(alias: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.INVALID_TRANSITION_ALIAS,\n    ngDevMode && `The transition alias value \"${alias}\" is not supported`,\n  );\n}\n\nexport function validationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.VALIDATION_FAILED,\n    ngDevMode && `animation validation failed:\\n${errors.map((err) => err.message).join('\\n')}`,\n  );\n}\n\nexport function buildingFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.BUILDING_FAILED,\n    ngDevMode && `animation building failed:\\n${errors.map((err) => err.message).join('\\n')}`,\n  );\n}\n\nexport function triggerBuildFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_BUILD_FAILED,\n    ngDevMode &&\n      `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors\n        .map((err) => err.message)\n        .join('\\n - ')}`,\n  );\n}\n\nexport function animationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.ANIMATION_FAILED,\n    ngDevMode &&\n      `Unable to animate due to the following errors:${LINE_START}${errors\n        .map((err) => err.message)\n        .join(LINE_START)}`,\n  );\n}\n\nexport function registerFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.REGISTRATION_FAILED,\n    ngDevMode &&\n      `Unable to build the animation due to the following errors: ${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function missingOrDestroyedAnimation(): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION,\n    ngDevMode && \"The requested animation doesn't exist or has already been destroyed\",\n  );\n}\n\nexport function createAnimationFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.CREATE_ANIMATION_FAILED,\n    ngDevMode &&\n      `Unable to create the animation due to the following errors:${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function missingPlayer(id: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_PLAYER,\n    ngDevMode && `Unable to find the timeline player referenced by ${id}`,\n  );\n}\n\nexport function missingTrigger(phase: string, name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_TRIGGER,\n    ngDevMode &&\n      `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`,\n  );\n}\n\nexport function missingEvent(name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.MISSING_EVENT,\n    ngDevMode &&\n      `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`,\n  );\n}\n\nexport function unsupportedTriggerEvent(phase: string, name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT,\n    ngDevMode &&\n      `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`,\n  );\n}\n\nexport function unregisteredTrigger(name: string): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.UNREGISTERED_TRIGGER,\n    ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`,\n  );\n}\n\nexport function triggerTransitionsFailed(errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED,\n    ngDevMode &&\n      `Unable to process animations due to the following failed trigger transitions\\n ${errors\n        .map((err) => err.message)\n        .join('\\n')}`,\n  );\n}\n\nexport function triggerParsingFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRIGGER_PARSING_FAILED,\n    ngDevMode &&\n      `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors\n        .map((err) => err.message)\n        .join(LINE_START)}`,\n  );\n}\n\nexport function transitionFailed(name: string, errors: Error[]): Error {\n  return new RuntimeError(\n    RuntimeErrorCode.TRANSITION_FAILED,\n    ngDevMode && `@${name} has failed due to:\\n ${errors.map((err) => err.message).join('\\n- ')}`,\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nexport const ANIMATABLE_PROP_SET = new Set([\n  '-moz-outline-radius',\n  '-moz-outline-radius-bottomleft',\n  '-moz-outline-radius-bottomright',\n  '-moz-outline-radius-topleft',\n  '-moz-outline-radius-topright',\n  '-ms-grid-columns',\n  '-ms-grid-rows',\n  '-webkit-line-clamp',\n  '-webkit-text-fill-color',\n  '-webkit-text-stroke',\n  '-webkit-text-stroke-color',\n  'accent-color',\n  'all',\n  'backdrop-filter',\n  'background',\n  'background-color',\n  'background-position',\n  'background-size',\n  'block-size',\n  'border',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-width',\n  'border-color',\n  'border-end-end-radius',\n  'border-end-start-radius',\n  'border-image-outset',\n  'border-image-slice',\n  'border-image-width',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-width',\n  'border-left',\n  'border-left-color',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-width',\n  'border-start-end-radius',\n  'border-start-start-radius',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-shadow',\n  'caret-color',\n  'clip',\n  'clip-path',\n  'color',\n  'column-count',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-width',\n  'column-width',\n  'columns',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-grow',\n  'flex-shrink',\n  'font',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-variation-settings',\n  'font-weight',\n  'gap',\n  'grid-column-gap',\n  'grid-gap',\n  'grid-row-gap',\n  'grid-template-columns',\n  'grid-template-rows',\n  'height',\n  'inline-size',\n  'input-security',\n  'inset',\n  'inset-block',\n  'inset-block-end',\n  'inset-block-start',\n  'inset-inline',\n  'inset-inline-end',\n  'inset-inline-start',\n  'left',\n  'letter-spacing',\n  'line-clamp',\n  'line-height',\n  'margin',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'mask',\n  'mask-border',\n  'mask-position',\n  'mask-size',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-lines',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'object-position',\n  'offset',\n  'offset-anchor',\n  'offset-distance',\n  'offset-path',\n  'offset-position',\n  'offset-rotate',\n  'opacity',\n  'order',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-width',\n  'padding',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'perspective',\n  'perspective-origin',\n  'right',\n  'rotate',\n  'row-gap',\n  'scale',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-coordinate',\n  'scroll-snap-destination',\n  'scrollbar-color',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'tab-size',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-thickness',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-indent',\n  'text-shadow',\n  'text-underline-offset',\n  'top',\n  'transform',\n  'transform-origin',\n  'translate',\n  'vertical-align',\n  'visibility',\n  'width',\n  'word-spacing',\n  'z-index',\n  'zoom',\n]);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimationEvent,\n  AnimationPlayer,\n  AUTO_STYLE,\n  NoopAnimationPlayer,\n  ɵAnimationGroupPlayer,\n  ɵPRE_STYLE as PRE_STYLE,\n  ɵStyleDataMap,\n} from '../../../src/animations';\n\nimport {AnimationStyleNormalizer} from '../../src/dsl/style_normalization/animation_style_normalizer';\nimport {animationFailed} from '../error_helpers';\n\nimport {ANIMATABLE_PROP_SET} from './web_animations/animatable_props_set';\n\nexport function optimizeGroupPlayer(players: AnimationPlayer[]): AnimationPlayer {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\n\nexport function normalizeKeyframes(\n  normalizer: AnimationStyleNormalizer,\n  keyframes: Array<ɵStyleDataMap>,\n  preStyles: ɵStyleDataMap = new Map(),\n  postStyles: ɵStyleDataMap = new Map(),\n): Array<ɵStyleDataMap> {\n  const errors: Error[] = [];\n  const normalizedKeyframes: Array<ɵStyleDataMap> = [];\n  let previousOffset = -1;\n  let previousKeyframe: ɵStyleDataMap | null = null;\n  keyframes.forEach((kf) => {\n    const offset = kf.get('offset') as number;\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe: ɵStyleDataMap = (isSameOffset && previousKeyframe) || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case PRE_STYLE:\n            normalizedValue = preStyles.get(prop)!;\n            break;\n\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop)!;\n            break;\n\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(\n              prop,\n              normalizedProp,\n              normalizedValue,\n              errors,\n            );\n            break;\n        }\n      }\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n\n  return normalizedKeyframes;\n}\n\nexport function listenOnPlayer(\n  player: AnimationPlayer,\n  eventName: string,\n  event: AnimationEvent | undefined,\n  callback: (event: any) => any,\n) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\n\nexport function copyAnimationEvent(\n  e: AnimationEvent,\n  phaseName: string,\n  player: AnimationPlayer,\n): AnimationEvent {\n  const totalTime = player.totalTime;\n  const disabled = (player as any).disabled ? true : false;\n  const event = makeAnimationEvent(\n    e.element,\n    e.triggerName,\n    e.fromState,\n    e.toState,\n    phaseName || e.phaseName,\n    totalTime == undefined ? e.totalTime : totalTime,\n    disabled,\n  );\n  const data = (e as any)['_data'];\n  if (data != null) {\n    (event as any)['_data'] = data;\n  }\n  return event;\n}\n\nexport function makeAnimationEvent(\n  element: any,\n  triggerName: string,\n  fromState: string,\n  toState: string,\n  phaseName: string = '',\n  totalTime: number = 0,\n  disabled?: boolean,\n): AnimationEvent {\n  return {element, triggerName, fromState, toState, phaseName, totalTime, disabled: !!disabled};\n}\n\nexport function getOrSetDefaultValue<T, V>(map: Map<T, V>, key: T, defaultValue: V) {\n  let value = map.get(key);\n  if (!value) {\n    map.set(key, (value = defaultValue));\n  }\n  return value;\n}\n\nexport function parseTimelineCommand(command: string): [string, string] {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\n\nconst documentElement: HTMLElement | null = /* @__PURE__ */ (() =>\n  typeof document === 'undefined' ? null : document.documentElement)();\n\nexport function getParentElement(element: any): unknown | null {\n  const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n  if (parent === documentElement) {\n    return null;\n  }\n  return parent;\n}\n\nfunction containsVendorPrefix(prop: string): boolean {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\n\nlet _CACHED_BODY: {style: any} | null = null;\nlet _IS_WEBKIT = false;\nexport function validateStyleProperty(prop: string): boolean {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY!.style ? 'WebkitAppearance' in _CACHED_BODY!.style : false;\n  }\n\n  let result = true;\n  if (_CACHED_BODY!.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY!.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY!.style;\n    }\n  }\n\n  return result;\n}\n\nexport function validateWebAnimatableStyleProperty(prop: string): boolean {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\n\nexport function getBodyNode(): any | null {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\n\nexport function containsElement(elm1: any, elm2: any): boolean {\n  while (elm2) {\n    if (elm2 === elm1) {\n      return true;\n    }\n    elm2 = getParentElement(elm2);\n  }\n  return false;\n}\n\nexport function invokeQuery(element: any, selector: string, multi: boolean): any[] {\n  if (multi) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  const elem = element.querySelector(selector);\n  return elem ? [elem] : [];\n}\n\nexport function hypenatePropsKeys(original: ɵStyleDataMap): ɵStyleDataMap {\n  const newMap: ɵStyleDataMap = new Map();\n  original.forEach((val, prop) => {\n    const newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n    newMap.set(newProp, val);\n  });\n  return newMap;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  AnimateTimings,\n  AnimationMetadata,\n  AnimationMetadataType,\n  AnimationOptions,\n  sequence,\n  ɵStyleData,\n  ɵStyleDataMap,\n} from '../../src/animations';\n\nimport {Ast as AnimationAst, AstVisitor as AnimationAstVisitor} from './dsl/animation_ast';\nimport {AnimationDslVisitor} from './dsl/animation_dsl_visitor';\nimport {\n  invalidNodeType,\n  invalidParamValue,\n  invalidStyleParams,\n  invalidTimingValue,\n  negativeDelayValue,\n  negativeStepValue,\n} from './error_helpers';\n\nconst ONE_SECOND = 1000;\n\nexport const SUBSTITUTION_EXPR_START = '{{';\nexport const SUBSTITUTION_EXPR_END = '}}';\nexport const ENTER_CLASSNAME = 'ng-enter';\nexport const LEAVE_CLASSNAME = 'ng-leave';\nexport const NG_TRIGGER_CLASSNAME = 'ng-trigger';\nexport const NG_TRIGGER_SELECTOR = '.ng-trigger';\nexport const NG_ANIMATING_CLASSNAME = 'ng-animating';\nexport const NG_ANIMATING_SELECTOR = '.ng-animating';\n\nexport function resolveTimingValue(value: string | number) {\n  if (typeof value == 'number') return value;\n\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\n\nfunction _convertTimeValueToMS(value: number, unit: string): number {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default: // ms or something else\n      return value;\n  }\n}\n\nexport function resolveTiming(\n  timings: string | number | AnimateTimings,\n  errors: Error[],\n  allowNegativeValues?: boolean,\n) {\n  return timings.hasOwnProperty('duration')\n    ? <AnimateTimings>timings\n    : parseTimeExpression(<string | number>timings, errors, allowNegativeValues);\n}\n\nfunction parseTimeExpression(\n  exp: string | number,\n  errors: Error[],\n  allowNegativeValues?: boolean,\n): AnimateTimings {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration: number;\n  let delay: number = 0;\n  let easing: string = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {duration: 0, delay: 0, easing: ''};\n    }\n\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n\n  return {duration, delay, easing};\n}\n\nexport function normalizeKeyframes(\n  keyframes: Array<ɵStyleData> | Array<ɵStyleDataMap>,\n): Array<ɵStyleDataMap> {\n  if (!keyframes.length) {\n    return [];\n  }\n  if (keyframes[0] instanceof Map) {\n    return keyframes as Array<ɵStyleDataMap>;\n  }\n  return keyframes.map((kf) => new Map(Object.entries(kf)));\n}\n\nexport function normalizeStyles(styles: ɵStyleDataMap | Array<ɵStyleDataMap>): ɵStyleDataMap {\n  return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\n\nexport function setStyles(element: any, styles: ɵStyleDataMap, formerStyles?: ɵStyleDataMap) {\n  styles.forEach((val, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    if (formerStyles && !formerStyles.has(prop)) {\n      formerStyles.set(prop, element.style[camelProp]);\n    }\n    element.style[camelProp] = val;\n  });\n}\n\nexport function eraseStyles(element: any, styles: ɵStyleDataMap) {\n  styles.forEach((_, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    element.style[camelProp] = '';\n  });\n}\n\nexport function normalizeAnimationEntry(\n  steps: AnimationMetadata | AnimationMetadata[],\n): AnimationMetadata {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps as AnimationMetadata;\n}\n\nexport function validateStyleParams(\n  value: string | number | null | undefined,\n  options: AnimationOptions,\n  errors: Error[],\n) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach((varName) => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\n\nconst PARAM_REGEX = /* @__PURE__ */ new RegExp(\n  `${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`,\n  'g',\n);\nexport function extractStyleParams(value: string | number | null | undefined): string[] {\n  let params: string[] = [];\n  if (typeof value === 'string') {\n    let match: any;\n    while ((match = PARAM_REGEX.exec(value))) {\n      params.push(match[1] as string);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\n\nexport function interpolateParams(\n  value: string | number,\n  params: {[name: string]: any},\n  errors: Error[],\n): string | number {\n  const original = `${value}`;\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\n\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nexport function dashCaseToCamelCase(input: string): string {\n  return input.replace(DASH_CASE_REGEXP, (...m: any[]) => m[1].toUpperCase());\n}\n\nexport function camelCaseToDashCase(input: string): string {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function allowPreviousPlayerStylesMerge(duration: number, delay: number) {\n  return duration === 0 || delay === 0;\n}\n\nexport function balancePreviousStylesIntoKeyframes(\n  element: any,\n  keyframes: Array<ɵStyleDataMap>,\n  previousStyles: ɵStyleDataMap,\n) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps: string[] = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe.set(prop, val);\n    });\n\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach((prop) => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n  return keyframes;\n}\n\nexport function visitDslNode(\n  visitor: AnimationDslVisitor,\n  node: AnimationMetadata,\n  context: any,\n): any;\nexport function visitDslNode(\n  visitor: AnimationAstVisitor,\n  node: AnimationAst<AnimationMetadataType>,\n  context: any,\n): any;\nexport function visitDslNode(visitor: any, node: any, context: any): any {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\n\nexport function computeStyle(element: any, prop: string): string {\n  return (<any>window.getComputedStyle(element))[prop];\n}\n"], "names": ["RuntimeError", "ɵAnimationGroupPlayer", "normalizeKeyframes", "PRE_STYLE"], "mappings": ";;;;;;;;;AAWA,MAAM,UAAU,GAAG,OAAO;AAEpB,SAAU,kBAAkB,CAAC,GAAoB,EAAA;IACrD,OAAO,IAAIA,aAAY,CAErB,IAAA,8CAAA,SAAS,IAAI,CAA8B,2BAAA,EAAA,GAAG,CAAe,aAAA,CAAA,CAC9D;AACH;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,6CAErB,SAAS,IAAI,kEAAkE,CAChF;AACH;SAEgB,kBAAkB,GAAA;AAChC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS,IAAI,+DAA+D,CAC7E;AACH;AAEM,SAAU,kBAAkB,CAAC,OAAe,EAAA;IAChD,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS;QACP,CAA+C,4CAAA,EAAA,OAAO,CAA8B,4BAAA,CAAA,CACvF;AACH;AAEM,SAAU,iBAAiB,CAAC,OAAe,EAAA;IAC/C,OAAO,IAAIA,aAAY,CAErB,IAAA,6CAAA,SAAS,IAAI,CAAkD,+CAAA,EAAA,OAAO,CAAE,CAAA,CACzE;AACH;AAEM,SAAU,eAAe,CAAC,QAAgB,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAErB,IAAA,2CAAA,SAAS,IAAI,CAA8C,2CAAA,EAAA,QAAQ,CAAE,CAAA,CACtE;AACH;AAEgB,SAAA,mBAAmB,CAAC,oBAA4B,EAAE,KAAa,EAAA;IAC7E,OAAO,IAAIA,aAAY,CAAA,IAAA,gDAErB,SAAS,IAAI,CAAuC,oCAAA,EAAA,oBAAoB,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA,CACpF;AACH;SAEgB,cAAc,GAAA;IAC5B,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS;AACP,QAAA,sFAAsF,CACzF;AACH;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,4CAErB,SAAS,IAAI,yEAAyE,CACvF;AACH;AAEgB,SAAA,YAAY,CAAC,YAAoB,EAAE,WAAqB,EAAA;IACtE,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;QACP,CAAU,OAAA,EAAA,YAAY,CAAiF,8EAAA,EAAA,WAAW,CAAC,IAAI,CACrH,IAAI,CACL,CAAE,CAAA,CACN;AACH;AAEM,SAAU,iBAAiB,CAAC,KAAa,EAAA;IAC7C,OAAO,IAAIA,aAAY,CAErB,IAAA,6CAAA,SAAS,IAAI,CAAmC,gCAAA,EAAA,KAAK,CAAkB,gBAAA,CAAA,CACxE;AACH;AAUM,SAAU,wBAAwB,CACtC,IAAY,EACZ,UAAkB,EAClB,QAAgB,EAChB,WAAmB,EACnB,SAAiB,EAAA;IAEjB,OAAO,IAAIA,aAAY,CAAA,IAAA,oDAErB,SAAS;QACP,CAAqB,kBAAA,EAAA,IAAI,CAAuC,oCAAA,EAAA,UAAU,CAAY,SAAA,EAAA,QAAQ,CAA4E,yEAAA,EAAA,WAAW,CAAY,SAAA,EAAA,SAAS,CAAK,GAAA,CAAA,CAClN;AACH;SAEgB,gBAAgB,GAAA;AAC9B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,2CAErB,SAAS,IAAI,CAAA,wDAAA,CAA0D,CACxE;AACH;SAEgB,aAAa,GAAA;AAC3B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,wCAErB,SAAS,IAAI,CAAA,2DAAA,CAA6D,CAC3E;AACH;SAEgB,yBAAyB,GAAA;AACvC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,uDAErB,SAAS,IAAI,CAAA,oDAAA,CAAsD,CACpE;AACH;SAEgB,uBAAuB,GAAA;AACrC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,mDAErB,SAAS,IAAI,CAAA,qEAAA,CAAuE,CACrF;AACH;SAEgB,cAAc,GAAA;AAC5B,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS,IAAI,CAAA,4CAAA,CAA8C,CAC5D;AACH;AAEM,SAAU,YAAY,CAAC,QAAgB,EAAA;IAC3C,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;AACP,QAAA,CAAA,SAAA,EAAY,QAAQ,CAAA,2CAAA,EAA8C,QAAQ,CAAA,oDAAA,CAAsD,CACnI;AACH;AAEM,SAAU,iBAAiB,CAAC,IAAY,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAErB,IAAA,4CAAA,SAAS,IAAI,CAAuC,oCAAA,EAAA,IAAI,CAAoB,kBAAA,CAAA,CAC7E;AACH;AAEM,SAAU,sBAAsB,CAAC,KAAa,EAAA;IAClD,OAAO,IAAIA,aAAY,CAErB,IAAA,kDAAA,SAAS,IAAI,CAA+B,4BAAA,EAAA,KAAK,CAAoB,kBAAA,CAAA,CACtE;AACH;AAEM,SAAU,gBAAgB,CAAC,MAAe,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAAA,IAAA,2CAErB,SAAS,IAAI,CAAiC,8BAAA,EAAA,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAC5F;AACH;AAEM,SAAU,cAAc,CAAC,MAAe,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS,IAAI,CAA+B,4BAAA,EAAA,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAA,CAC1F;AACH;AAEgB,SAAA,kBAAkB,CAAC,IAAY,EAAE,MAAe,EAAA;IAC9D,OAAO,IAAIA,aAAY,CAAA,IAAA,8CAErB,SAAS;QACP,CAA0B,uBAAA,EAAA,IAAI,0DAA0D;aACrF,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;AACxB,aAAA,IAAI,CAAC,OAAO,CAAC,CAAA,CAAE,CACrB;AACH;AAEM,SAAU,eAAe,CAAC,MAAe,EAAA;IAC7C,OAAO,IAAIA,aAAY,CAAA,IAAA,0CAErB,SAAS;QACP,CAAiD,8CAAA,EAAA,UAAU,GAAG;aAC3D,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;AACxB,aAAA,IAAI,CAAC,UAAU,CAAC,CAAA,CAAE,CACxB;AACH;AAEM,SAAU,cAAc,CAAC,MAAe,EAAA;IAC5C,OAAO,IAAIA,aAAY,CAAA,IAAA,6CAErB,SAAS;AACP,QAAA,CAAA,2DAAA,EAA8D;aAC3D,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;AACxB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB;AACH;SAEgB,2BAA2B,GAAA;AACzC,IAAA,OAAO,IAAIA,aAAY,CAAA,IAAA,wDAErB,SAAS,IAAI,qEAAqE,CACnF;AACH;AAEM,SAAU,qBAAqB,CAAC,MAAe,EAAA;IACnD,OAAO,IAAIA,aAAY,CAAA,IAAA,iDAErB,SAAS;AACP,QAAA,CAAA,2DAAA,EAA8D;aAC3D,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;AACxB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB;AACH;AAEM,SAAU,aAAa,CAAC,EAAU,EAAA;IACtC,OAAO,IAAIA,aAAY,CAErB,IAAA,wCAAA,SAAS,IAAI,CAAoD,iDAAA,EAAA,EAAE,CAAE,CAAA,CACtE;AACH;AAEgB,SAAA,cAAc,CAAC,KAAa,EAAE,IAAY,EAAA;IACxD,OAAO,IAAIA,aAAY,CAAA,IAAA,yCAErB,SAAS;AACP,QAAA,CAAA,iDAAA,EAAoD,KAAK,CAAA,iCAAA,EAAoC,IAAI,CAAA,iBAAA,CAAmB,CACvH;AACH;AAEM,SAAU,YAAY,CAAC,IAAY,EAAA;IACvC,OAAO,IAAIA,aAAY,CAAA,IAAA,uCAErB,SAAS;QACP,CAA8C,2CAAA,EAAA,IAAI,CAA4C,0CAAA,CAAA,CACjG;AACH;AAEgB,SAAA,uBAAuB,CAAC,KAAa,EAAE,IAAY,EAAA;IACjE,OAAO,IAAIA,aAAY,CAAA,IAAA,mDAErB,SAAS;AACP,QAAA,CAAA,sCAAA,EAAyC,KAAK,CAAA,6BAAA,EAAgC,IAAI,CAAA,mBAAA,CAAqB,CAC1G;AACH;AAEM,SAAU,mBAAmB,CAAC,IAAY,EAAA;IAC9C,OAAO,IAAIA,aAAY,CAErB,IAAA,8CAAA,SAAS,IAAI,CAAmC,gCAAA,EAAA,IAAI,CAA4B,0BAAA,CAAA,CACjF;AACH;AAEM,SAAU,wBAAwB,CAAC,MAAe,EAAA;IACtD,OAAO,IAAIA,aAAY,CAAA,IAAA,oDAErB,SAAS;AACP,QAAA,CAAA,+EAAA,EAAkF;aAC/E,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;AACxB,aAAA,IAAI,CAAC,IAAI,CAAC,CAAA,CAAE,CAClB;AACH;AAYgB,SAAA,gBAAgB,CAAC,IAAY,EAAE,MAAe,EAAA;AAC5D,IAAA,OAAO,IAAIA,aAAY,CAErB,IAAA,2CAAA,SAAS,IAAI,CAAA,CAAA,EAAI,IAAI,CAAA,sBAAA,EAAyB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAE,CAAA,CAC9F;AACH;;AClSA;;;;AAIG;AACI,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC;IACzC,qBAAqB;IACrB,gCAAgC;IAChC,iCAAiC;IACjC,6BAA6B;IAC7B,8BAA8B;IAC9B,kBAAkB;IAClB,eAAe;IACf,oBAAoB;IACpB,yBAAyB;IACzB,qBAAqB;IACrB,2BAA2B;IAC3B,cAAc;IACd,KAAK;IACL,iBAAiB;IACjB,YAAY;IACZ,kBAAkB;IAClB,qBAAqB;IACrB,iBAAiB;IACjB,YAAY;IACZ,QAAQ;IACR,kBAAkB;IAClB,wBAAwB;IACxB,wBAAwB;IACxB,oBAAoB;IACpB,0BAA0B;IAC1B,0BAA0B;IAC1B,eAAe;IACf,qBAAqB;IACrB,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,cAAc;IACd,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,oBAAoB;IACpB,oBAAoB;IACpB,mBAAmB;IACnB,yBAAyB;IACzB,yBAAyB;IACzB,qBAAqB;IACrB,2BAA2B;IAC3B,2BAA2B;IAC3B,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,yBAAyB;IACzB,2BAA2B;IAC3B,YAAY;IACZ,kBAAkB;IAClB,wBAAwB;IACxB,yBAAyB;IACzB,kBAAkB;IAClB,cAAc;IACd,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,MAAM;IACN,WAAW;IACX,OAAO;IACP,cAAc;IACd,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,cAAc;IACd,SAAS;IACT,QAAQ;IACR,MAAM;IACN,YAAY;IACZ,WAAW;IACX,aAAa;IACb,MAAM;IACN,WAAW;IACX,kBAAkB;IAClB,cAAc;IACd,yBAAyB;IACzB,aAAa;IACb,KAAK;IACL,iBAAiB;IACjB,UAAU;IACV,cAAc;IACd,uBAAuB;IACvB,oBAAoB;IACpB,QAAQ;IACR,aAAa;IACb,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,iBAAiB;IACjB,mBAAmB;IACnB,cAAc;IACd,kBAAkB;IAClB,oBAAoB;IACpB,MAAM;IACN,gBAAgB;IAChB,YAAY;IACZ,aAAa;IACb,QAAQ;IACR,kBAAkB;IAClB,oBAAoB;IACpB,eAAe;IACf,mBAAmB;IACnB,qBAAqB;IACrB,aAAa;IACb,cAAc;IACd,YAAY;IACZ,MAAM;IACN,aAAa;IACb,eAAe;IACf,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,WAAW;IACX,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,eAAe;IACf,iBAAiB;IACjB,aAAa;IACb,iBAAiB;IACjB,eAAe;IACf,SAAS;IACT,OAAO;IACP,SAAS;IACT,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,SAAS;IACT,mBAAmB;IACnB,qBAAqB;IACrB,gBAAgB;IAChB,oBAAoB;IACpB,sBAAsB;IACtB,cAAc;IACd,eAAe;IACf,aAAa;IACb,aAAa;IACb,oBAAoB;IACpB,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,eAAe;IACf,qBAAqB;IACrB,yBAAyB;IACzB,2BAA2B;IAC3B,sBAAsB;IACtB,sBAAsB;IACtB,0BAA0B;IAC1B,4BAA4B;IAC5B,oBAAoB;IACpB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,sBAAsB;IACtB,0BAA0B;IAC1B,4BAA4B;IAC5B,uBAAuB;IACvB,uBAAuB;IACvB,2BAA2B;IAC3B,6BAA6B;IAC7B,qBAAqB;IACrB,sBAAsB;IACtB,oBAAoB;IACpB,wBAAwB;IACxB,yBAAyB;IACzB,iBAAiB;IACjB,uBAAuB;IACvB,cAAc;IACd,eAAe;IACf,UAAU;IACV,iBAAiB;IACjB,uBAAuB;IACvB,2BAA2B;IAC3B,eAAe;IACf,qBAAqB;IACrB,aAAa;IACb,aAAa;IACb,uBAAuB;IACvB,KAAK;IACL,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,gBAAgB;IAChB,YAAY;IACZ,OAAO;IACP,cAAc;IACd,SAAS;IACT,MAAM;AACP,CAAA,CAAC;;AC/LI,SAAU,mBAAmB,CAAC,OAA0B,EAAA;AAC5D,IAAA,QAAQ,OAAO,CAAC,MAAM;AACpB,QAAA,KAAK,CAAC;YACJ,OAAO,IAAI,mBAAmB,EAAE;AAClC,QAAA,KAAK,CAAC;AACJ,YAAA,OAAO,OAAO,CAAC,CAAC,CAAC;AACnB,QAAA;AACE,YAAA,OAAO,IAAIC,oBAAqB,CAAC,OAAO,CAAC;;AAE/C;AAEgB,SAAAC,oBAAkB,CAChC,UAAoC,EACpC,SAA+B,EAC/B,SAA2B,GAAA,IAAI,GAAG,EAAE,EACpC,UAA4B,GAAA,IAAI,GAAG,EAAE,EAAA;IAErC,MAAM,MAAM,GAAY,EAAE;IAC1B,MAAM,mBAAmB,GAAyB,EAAE;AACpD,IAAA,IAAI,cAAc,GAAG,EAAE;IACvB,IAAI,gBAAgB,GAAyB,IAAI;AACjD,IAAA,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;QACvB,MAAM,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAW;AACzC,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,cAAc;QAC7C,MAAM,kBAAkB,GAAkB,CAAC,YAAY,IAAI,gBAAgB,KAAK,IAAI,GAAG,EAAE;QACzF,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACvB,IAAI,cAAc,GAAG,IAAI;YACzB,IAAI,eAAe,GAAG,GAAG;AACzB,YAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,cAAc,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC;gBACzE,QAAQ,eAAe;AACrB,oBAAA,KAAKC,UAAS;AACZ,wBAAA,eAAe,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAE;wBACtC;AAEF,oBAAA,KAAK,UAAU;AACb,wBAAA,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAE;wBACvC;AAEF,oBAAA;AACE,wBAAA,eAAe,GAAG,UAAU,CAAC,mBAAmB,CAC9C,IAAI,EACJ,cAAc,EACd,eAAe,EACf,MAAM,CACP;wBACD;;;AAGN,YAAA,kBAAkB,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;AACzD,SAAC,CAAC;QACF,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC;;QAE9C,gBAAgB,GAAG,kBAAkB;QACrC,cAAc,GAAG,MAAM;AACzB,KAAC,CAAC;AACF,IAAA,IAAI,MAAM,CAAC,MAAM,EAAE;AACjB,QAAA,MAAM,eAAe,CAAC,MAAM,CAAC;;AAG/B,IAAA,OAAO,mBAAmB;AAC5B;AAEM,SAAU,cAAc,CAC5B,MAAuB,EACvB,SAAiB,EACjB,KAAiC,EACjC,QAA6B,EAAA;IAE7B,QAAQ,SAAS;AACf,QAAA,KAAK,OAAO;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACnF;AACF,QAAA,KAAK,MAAM;YACT,MAAM,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YACjF;AACF,QAAA,KAAK,SAAS;YACZ,MAAM,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;YACvF;;AAEN;SAEgB,kBAAkB,CAChC,CAAiB,EACjB,SAAiB,EACjB,MAAuB,EAAA;AAEvB,IAAA,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS;AAClC,IAAA,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,GAAG,IAAI,GAAG,KAAK;AACxD,IAAA,MAAM,KAAK,GAAG,kBAAkB,CAC9B,CAAC,CAAC,OAAO,EACT,CAAC,CAAC,WAAW,EACb,CAAC,CAAC,SAAS,EACX,CAAC,CAAC,OAAO,EACT,SAAS,IAAI,CAAC,CAAC,SAAS,EACxB,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,EAChD,QAAQ,CACT;AACD,IAAA,MAAM,IAAI,GAAI,CAAS,CAAC,OAAO,CAAC;AAChC,IAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AACf,QAAA,KAAa,CAAC,OAAO,CAAC,GAAG,IAAI;;AAEhC,IAAA,OAAO,KAAK;AACd;SAEgB,kBAAkB,CAChC,OAAY,EACZ,WAAmB,EACnB,SAAiB,EACjB,OAAe,EACf,SAAoB,GAAA,EAAE,EACtB,SAAoB,GAAA,CAAC,EACrB,QAAkB,EAAA;AAElB,IAAA,OAAO,EAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAC;AAC/F;SAEgB,oBAAoB,CAAO,GAAc,EAAE,GAAM,EAAE,YAAe,EAAA;IAChF,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACxB,IAAI,CAAC,KAAK,EAAE;QACV,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,YAAY,EAAE;;AAEtC,IAAA,OAAO,KAAK;AACd;AAEM,SAAU,oBAAoB,CAAC,OAAe,EAAA;IAClD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;IACzC,MAAM,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC;IAC7C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AAC9C,IAAA,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC;AACrB;AAEA,MAAM,eAAe,mBAAuC,CAAC,MAC3D,OAAO,QAAQ,KAAK,WAAW,GAAG,IAAI,GAAG,QAAQ,CAAC,eAAe,GAAG;AAEhE,SAAU,gBAAgB,CAAC,OAAY,EAAA;AAC3C,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;AAC1D,IAAA,IAAI,MAAM,KAAK,eAAe,EAAE;AAC9B,QAAA,OAAO,IAAI;;AAEb,IAAA,OAAO,MAAM;AACf;AAEA,SAAS,oBAAoB,CAAC,IAAY,EAAA;;;AAGxC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC;AACzC;AAEA,IAAI,YAAY,GAAwB,IAAI;AAC5C,IAAI,UAAU,GAAG,KAAK;AAChB,SAAU,qBAAqB,CAAC,IAAY,EAAA;IAChD,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,YAAY,GAAG,WAAW,EAAE,IAAI,EAAE;AAClC,QAAA,UAAU,GAAG,YAAa,CAAC,KAAK,GAAG,kBAAkB,IAAI,YAAa,CAAC,KAAK,GAAG,KAAK;;IAGtF,IAAI,MAAM,GAAG,IAAI;IACjB,IAAI,YAAa,CAAC,KAAK,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AACtD,QAAA,MAAM,GAAG,IAAI,IAAI,YAAa,CAAC,KAAK;AACpC,QAAA,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;YACzB,MAAM,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,YAAA,MAAM,GAAG,SAAS,IAAI,YAAa,CAAC,KAAK;;;AAI7C,IAAA,OAAO,MAAM;AACf;AAEM,SAAU,kCAAkC,CAAC,IAAY,EAAA;AAC7D,IAAA,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;AACtC;SAEgB,WAAW,GAAA;AACzB,IAAA,IAAI,OAAO,QAAQ,IAAI,WAAW,EAAE;QAClC,OAAO,QAAQ,CAAC,IAAI;;AAEtB,IAAA,OAAO,IAAI;AACb;AAEgB,SAAA,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;IAClD,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,YAAA,OAAO,IAAI;;AAEb,QAAA,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC;;AAE/B,IAAA,OAAO,KAAK;AACd;SAEgB,WAAW,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;IACxE,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;;IAEvD,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC5C,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE;AAC3B;;AC/LA,MAAM,UAAU,GAAG,IAAI;AAEhB,MAAM,uBAAuB,GAAG;AAChC,MAAM,qBAAqB,GAAG,IAAI;AAClC,MAAM,eAAe,GAAG;AACxB,MAAM,eAAe,GAAG;AACxB,MAAM,oBAAoB,GAAG;AAC7B,MAAM,mBAAmB,GAAG;AAC5B,MAAM,sBAAsB,GAAG;AAC/B,MAAM,qBAAqB,GAAG;AAE/B,SAAU,kBAAkB,CAAC,KAAsB,EAAA;IACvD,IAAI,OAAO,KAAK,IAAI,QAAQ;AAAE,QAAA,OAAO,KAAK;IAE1C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;AAChD,IAAA,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;AAAE,QAAA,OAAO,CAAC;AAE5C,IAAA,OAAO,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAClE;AAEA,SAAS,qBAAqB,CAAC,KAAa,EAAE,IAAY,EAAA;IACxD,QAAQ,IAAI;AACV,QAAA,KAAK,GAAG;YACN,OAAO,KAAK,GAAG,UAAU;AAC3B,QAAA;AACE,YAAA,OAAO,KAAK;;AAElB;SAEgB,aAAa,CAC3B,OAAyC,EACzC,MAAe,EACf,mBAA6B,EAAA;AAE7B,IAAA,OAAO,OAAO,CAAC,cAAc,CAAC,UAAU;AACtC,UAAkB;UAChB,mBAAmB,CAAkB,OAAO,EAAE,MAAM,EAAE,mBAAmB,CAAC;AAChF;AAEA,SAAS,mBAAmB,CAC1B,GAAoB,EACpB,MAAe,EACf,mBAA6B,EAAA;IAE7B,MAAM,KAAK,GAAG,0EAA0E;AACxF,IAAA,IAAI,QAAgB;IACpB,IAAI,KAAK,GAAW,CAAC;IACrB,IAAI,MAAM,GAAW,EAAE;AACvB,IAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AAChC,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AACpC,YAAA,OAAO,EAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAC;;AAG5C,QAAA,QAAQ,GAAG,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAEpE,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;AAC7B,QAAA,IAAI,UAAU,IAAI,IAAI,EAAE;AACtB,YAAA,KAAK,GAAG,qBAAqB,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;;AAGnE,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,EAAE;YACb,MAAM,GAAG,SAAS;;;SAEf;QACL,QAAQ,GAAG,GAAG;;IAGhB,IAAI,CAAC,mBAAmB,EAAE;QACxB,IAAI,cAAc,GAAG,KAAK;AAC1B,QAAA,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM;AAC9B,QAAA,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChB,YAAA,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAChC,cAAc,GAAG,IAAI;;AAEvB,QAAA,IAAI,KAAK,GAAG,CAAC,EAAE;AACb,YAAA,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjC,cAAc,GAAG,IAAI;;QAEvB,IAAI,cAAc,EAAE;AAClB,YAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;;;AAIzD,IAAA,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAC;AAClC;AAEM,SAAU,kBAAkB,CAChC,SAAmD,EAAA;AAEnD,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACrB,QAAA,OAAO,EAAE;;AAEX,IAAA,IAAI,SAAS,CAAC,CAAC,CAAC,YAAY,GAAG,EAAE;AAC/B,QAAA,OAAO,SAAiC;;IAE1C,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D;AAEM,SAAU,eAAe,CAAC,MAA4C,EAAA;IAC1E,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;AACrE;SAEgB,SAAS,CAAC,OAAY,EAAE,MAAqB,EAAE,YAA4B,EAAA;IACzF,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;AAC3B,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC;QAC3C,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC3C,YAAA,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;AAElD,QAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,GAAG;AAChC,KAAC,CAAC;AACJ;AAEgB,SAAA,WAAW,CAAC,OAAY,EAAE,MAAqB,EAAA;IAC7D,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAI;AACzB,QAAA,MAAM,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC;AAC3C,QAAA,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;AAC/B,KAAC,CAAC;AACJ;AAEM,SAAU,uBAAuB,CACrC,KAA8C,EAAA;AAE9C,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAAE,YAAA,OAAO,KAAK,CAAC,CAAC,CAAC;AACtC,QAAA,OAAO,QAAQ,CAAC,KAAK,CAAC;;AAExB,IAAA,OAAO,KAA0B;AACnC;SAEgB,mBAAmB,CACjC,KAAyC,EACzC,OAAyB,EACzB,MAAe,EAAA;AAEf,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE;AACnC,IAAA,MAAM,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC;AACzC,IAAA,IAAI,OAAO,CAAC,MAAM,EAAE;AAClB,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;YAC1B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACnC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;;AAE5C,SAAC,CAAC;;AAEN;AAEA,MAAM,WAAW,mBAAmB,IAAI,MAAM,CAC5C,CAAG,EAAA,uBAAuB,gBAAgB,qBAAqB,CAAA,CAAE,EACjE,GAAG,CACJ;AACK,SAAU,kBAAkB,CAAC,KAAyC,EAAA;IAC1E,IAAI,MAAM,GAAa,EAAE;AACzB,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,IAAI,KAAU;QACd,QAAQ,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;YACxC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAW,CAAC;;AAEjC,QAAA,WAAW,CAAC,SAAS,GAAG,CAAC;;AAE3B,IAAA,OAAO,MAAM;AACf;SAEgB,iBAAiB,CAC/B,KAAsB,EACtB,MAA6B,EAC7B,MAAe,EAAA;AAEf,IAAA,MAAM,QAAQ,GAAG,CAAG,EAAA,KAAK,EAAE;AAC3B,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,KAAI;AACvD,QAAA,IAAI,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;;AAE9B,QAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACvC,QAAQ,GAAG,EAAE;;AAEf,QAAA,OAAO,QAAQ,CAAC,QAAQ,EAAE;AAC5B,KAAC,CAAC;;IAGF,OAAO,GAAG,IAAI,QAAQ,GAAG,KAAK,GAAG,GAAG;AACtC;AAEA,MAAM,gBAAgB,GAAG,eAAe;AAClC,SAAU,mBAAmB,CAAC,KAAa,EAAA;IAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;AAC7E;AAEM,SAAU,mBAAmB,CAAC,KAAa,EAAA;IAC/C,OAAO,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE;AAChE;AAEgB,SAAA,8BAA8B,CAAC,QAAgB,EAAE,KAAa,EAAA;AAC5E,IAAA,OAAO,QAAQ,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC;AACtC;SAEgB,kCAAkC,CAChD,OAAY,EACZ,SAA+B,EAC/B,cAA6B,EAAA;IAE7B,IAAI,cAAc,CAAC,IAAI,IAAI,SAAS,CAAC,MAAM,EAAE;AAC3C,QAAA,IAAI,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC;QACnC,IAAI,iBAAiB,GAAa,EAAE;QACpC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAI;YACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/B,gBAAA,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;;AAE9B,YAAA,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AACjC,SAAC,CAAC;AAEF,QAAA,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC5B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAA,IAAI,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC;gBACrB,iBAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;;;;AAIpF,IAAA,OAAO,SAAS;AAClB;SAYgB,YAAY,CAAC,OAAY,EAAE,IAAS,EAAE,OAAY,EAAA;AAChE,IAAA,QAAQ,IAAI,CAAC,IAAI;QACf,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;QAC5C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QAC1C,KAAK,qBAAqB,CAAC,UAAU;YACnC,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;QAC/C,KAAK,qBAAqB,CAAC,QAAQ;YACjC,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC;QAC7C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QAC1C,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;QAC5C,KAAK,qBAAqB,CAAC,SAAS;YAClC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC;QAC9C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QAC1C,KAAK,qBAAqB,CAAC,SAAS;YAClC,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC;QAC9C,KAAK,qBAAqB,CAAC,YAAY;YACrC,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC;QACjD,KAAK,qBAAqB,CAAC,UAAU;YACnC,OAAO,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;QAC/C,KAAK,qBAAqB,CAAC,KAAK;YAC9B,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;QAC1C,KAAK,qBAAqB,CAAC,OAAO;YAChC,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC;AAC5C,QAAA;AACE,YAAA,MAAM,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEtC;AAEgB,SAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAA;IACrD,OAAa,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAE,CAAC,IAAI,CAAC;AACtD;;;;"}